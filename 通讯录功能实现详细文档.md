# 腾讯IM TUIKit通讯录功能改造实现文档

## 概述
本文档详细说明了如何将腾讯IM TUIKit的好友数据获取方式从SDK自动获取改为基于接口的通讯录数据获取，并实现班级切换功能。

## 1. 改造目标

### 1.1 原有方案问题
- **数据来源**: 依赖腾讯IM SDK自动获取好友数据
- **数据结构**: 固定的好友关系模式
- **业务适配**: 无法满足学校通讯录的业务需求

### 1.2 新方案优势
- **数据来源**: 基于业务接口获取通讯录数据
- **角色适配**: 根据用户角色（家长/老师）调用不同接口
- **班级管理**: 支持班级切换和学生列表展示
- **业务贴合**: 完全符合学校通讯录业务场景

## 2. 技术架构设计

### 2.1 整体架构
```
用户登录 → 角色识别 → 接口调用 → 数据适配 → UI渲染 → 班级切换
```

### 2.2 核心组件
1. **AddressBookService**: 通讯录数据获取服务
2. **ContactList组件**: 联系人列表展示组件
3. **数据适配层**: 将接口数据转换为TUIKit兼容格式

## 3. 接口字段差异说明

### 3.1 不同角色的接口字段
由于家长和老师使用不同的接口，返回的数据字段也有差异：

#### 家长接口 (`/app/addressBookApp/getElternAddressBook`)
```json
{
    "adlist": [
        {
            "id": "784148883587465218",
            "name": "3班",
            "studentList": [...]
        }
    ],
    "schoolBadgeUrl": "",
    "schoolName": "学校名称"
}
```

#### 老师接口 (`/app/addressBookApp/getEmployeeAddressBook`)
```json
{
    "classAddBookDTOList": [
        {
            "id": "784148883587465218",
            "name": "3班",
            "studentList": [...]
        }
    ],
    "schoolBadgeUrl": "",
    "schoolName": "学校名称"
}
```

**关键差异**：
- 家长接口：班级列表字段为 `adlist`
- 老师接口：班级列表字段为 `classAddBookDTOList`
- 其他字段结构完全相同

## 4. 详细实现

### 4.1 创建通讯录服务 (AddressBookService)

**文件位置**: `src/TUIKit/services/addressBookService.js`

#### 核心功能
```javascript
class AddressBookService {
    // 根据用户角色获取通讯录数据
    async getAddressBookByRole(roleCode) {
        let url = ''
        if (roleCode === 'eltern') {
            url = '/app/addressBookApp/getElternAddressBook'  // 家长接口
        } else {
            url = '/app/addressBookApp/getEmployeeAddressBook' // 老师接口
        }
        
        const response = await http.get(url)
        return this.formatAddressBookData(response.data)
    }
    
    // 格式化通讯录数据 - 支持不同角色的字段差异
    formatAddressBookData(rawData, roleCode) {
        // 根据角色获取不同的字段
        let classList = []
        let schoolBadgeUrl = ''
        let schoolName = ''

        if (roleCode === 'eltern') {
            // 家长接口字段: adlist
            const { adlist = [], schoolBadgeUrl: badgeUrl = '', schoolName: name = '' } = rawData
            classList = adlist
            schoolBadgeUrl = badgeUrl
            schoolName = name
        } else {
            // 老师接口字段: classAddBookDTOList
            const { classAddBookDTOList = [], schoolBadgeUrl: badgeUrl = '', schoolName: name = '' } = rawData
            classList = classAddBookDTOList
            schoolBadgeUrl = badgeUrl
            schoolName = name
        }

        return {
            classList: classList.map(classItem => ({
                id: classItem.id,
                name: classItem.name,
                studentList: this.formatStudentList(classItem.studentList || [])
            })),
            schoolInfo: {
                badgeUrl: schoolBadgeUrl,
                name: schoolName
            }
        }
    }
    
    // 将学生数据适配为TUIKit Friend格式
    formatStudentList(studentList) {
        return studentList.map((student, index) => ({
            // TUIKit Friend 必需字段
            userID: student.id || `student_${index}`,
            profile: {
                userID: student.id || `student_${index}`,
                avatar: student.avatar || '',
                nick: student.name || '未知学生'
            },
            remark: student.name || '未知学生',
            
            // 扩展字段 - 学生特有信息
            studentInfo: {
                id: student.id,
                name: student.name,
                className: student.className,
                gender: student.gender,
                address: student.address,
                phone: student.phone,
                avatar: student.avatar
            },
            
            // 渲染相关字段
            renderKey: `student_${student.id || index}`,
            addTime: Date.now(),
            isStudent: true // 标识这是学生数据
        }))
    }
}
```

#### 关键设计点
1. **角色适配**: 根据`roleCode`自动选择对应的接口
2. **字段差异处理**: 家长接口使用`adlist`字段，老师接口使用`classAddBookDTOList`字段
3. **数据转换**: 将接口返回的学生数据转换为TUIKit期望的Friend格式
4. **扩展字段**: 保留学生的原始信息，便于后续使用
5. **错误处理**: 完善的异常捕获和日志记录

### 3.2 修改ContactList组件

**文件位置**: `src/TUIKit/components/TUIContact/contact-list/index.vue`

#### 主要改动点

##### 1. 导入依赖
```javascript
// 新增导入
import addressBookService from '../../../services/addressBookService.js';
import useStore from '@/store';
```

##### 2. 添加状态管理
```javascript
// 获取用户store
const { user } = useStore();

// 通讯录相关状态
const addressBookData = ref({
    classList: [],
    schoolInfo: { badgeUrl: '', name: '' }
});
const currentClassId = ref('');
const isLoadingAddressBook = ref(false);
```

##### 3. 计算属性
```javascript
// 当前班级信息
const currentClassInfo = computed(() => {
    if (!currentClassId.value || !addressBookData.value.classList.length) {
        return addressBookData.value.classList[0] || null;
    }
    return addressBookData.value.classList.find(cls => cls.id === currentClassId.value) || null;
});

// 班级选项列表
const classOptions = computed(() => {
    return addressBookData.value.classList.map(cls => ({
        label: cls.name,
        value: cls.id
    }));
});
```

##### 4. 核心方法实现
```javascript
// 初始化通讯录数据
async function initAddressBook() {
    try {
        isLoadingAddressBook.value = true;
        
        // 获取用户角色
        const roleCode = user.identityInfo?.roleCode || 'teacher';
        
        // 获取通讯录数据
        const data = await addressBookService.getAddressBookByRole(roleCode);
        addressBookData.value = data;
        
        // 设置默认班级（第一个班级）
        if (data.classList.length > 0) {
            currentClassId.value = data.classList[0].id;
            updateFriendListWithStudents(data.classList[0].studentList);
        }
    } catch (error) {
        console.error('[ContactList] 通讯录数据初始化失败:', error);
    } finally {
        isLoadingAddressBook.value = false;
    }
}

// 切换班级
function switchClass(classId) {
    currentClassId.value = classId;
    const students = addressBookService.getStudentsByClassId(addressBookData.value.classList, classId);
    updateFriendListWithStudents(students);
}

// 更新好友列表为学生数据
function updateFriendListWithStudents(students) {
    contactListMap.value.friendList.list = students;
    contactListMap.value.friendList.title = `我的好友 (${students.length})`;
    
    // 自动展开好友列表
    if (currentContactListKey.value !== 'friendList' && students.length > 0) {
        currentContactListKey.value = 'friendList';
        TUIStore.update(StoreName.CUSTOM, 'currentContactListKey', 'friendList');
    }
}
```

##### 5. 生命周期修改
```javascript
onMounted(() => {
    // 初始化通讯录数据
    initAddressBook();
    
    // 注释掉原有的好友数据监听
    // TUIStore.watch(StoreName.FRIEND, {
    //   friendList: onFriendListUpdated,
    //   ...
    // });
    
    // 保留其他监听器...
});
```

### 3.3 UI界面改造

#### 班级选择器
```vue
<template>
    <!-- 班级选择器 -->
    <div v-if="addressBookData.classList.length > 0" class="tui-class-selector">
        <div class="tui-class-selector-header">
            <span class="tui-class-selector-title">选择班级</span>
            <span v-if="addressBookData.schoolInfo.name" class="tui-class-selector-school">
                {{ addressBookData.schoolInfo.name }}
            </span>
        </div>
        <div class="tui-class-selector-options">
            <div
                v-for="classItem in addressBookData.classList"
                :key="classItem.id"
                :class="[
                    'tui-class-selector-option',
                    currentClassId === classItem.id ? 'active' : ''
                ]"
                @click="switchClass(classItem.id)"
            >
                {{ classItem.name }}
                <span class="tui-class-selector-count">({{ classItem.studentList.length }})</span>
            </div>
        </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="isLoadingAddressBook" class="tui-loading">
        <div class="tui-loading-text">正在加载通讯录...</div>
    </div>
    
    <!-- 原有的联系人列表 -->
    <ul v-if="!contactSearchingStatus && !isLoadingAddressBook" ...>
        <!-- 保持原有结构 -->
    </ul>
</template>
```

#### 样式设计
```scss
.tui-class-selector {
    padding: 16px;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    margin-bottom: 8px;

    &-options {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
    }

    &-option {
        padding: 8px 12px;
        background: #fff;
        border: 1px solid #ddd;
        border-radius: 6px;
        cursor: pointer;
        transition: all 0.2s ease;

        &.active {
            background: #007bff;
            border-color: #007bff;
            color: #fff;
        }
    }
}
```

### 3.4 类型定义扩展

**文件位置**: `src/TUIKit/interface.ts`

```typescript
// 扩展联系人信息类型，添加renderKey属性
export interface IContactInfoTypeExtended {
    renderKey?: string;
    [key: string]: any;
}

export type IContactInfoType = (Friend | FriendApplication | IGroupModel | IBlackListUserItem) & IContactInfoTypeExtended;
```

## 4. 数据流程图

```mermaid
graph TD
    A[用户登录] --> B[获取用户角色]
    B --> C{角色判断}
    C -->|家长| D[调用家长接口]
    C -->|老师| E[调用老师接口]
    D --> F[获取通讯录数据]
    E --> F
    F --> G[数据格式化]
    G --> H[适配TUIKit格式]
    H --> I[更新UI状态]
    I --> J[渲染班级选择器]
    J --> K[渲染学生列表]
    K --> L[用户切换班级]
    L --> M[更新学生列表]
    M --> K
```

## 5. 关键技术点

### 5.1 数据适配策略
1. **保持兼容性**: 新的学生数据结构完全兼容TUIKit的Friend接口
2. **扩展字段**: 通过`studentInfo`字段保留学生的完整信息
3. **渲染标识**: 使用`renderKey`确保列表渲染的唯一性

### 5.2 状态管理
1. **响应式数据**: 使用Vue3的ref和computed实现响应式更新
2. **状态同步**: 与TUIStore保持同步，确保组件间通信正常
3. **生命周期**: 在组件挂载时初始化数据，卸载时清理资源

### 5.3 错误处理
1. **接口异常**: 完善的try-catch机制
2. **数据校验**: 对接口返回数据进行格式验证
3. **用户反馈**: 通过loading状态和错误提示改善用户体验

## 6. 测试验证

### 6.1 功能测试
创建了专门的测试页面 `src/pages/test-addressbook/index.vue` 用于验证：
- 通讯录数据获取
- 班级切换功能
- 学生列表展示
- 错误处理机制

### 6.2 测试用例
1. **角色识别测试**: 验证不同角色调用不同接口
2. **数据格式测试**: 验证数据适配的正确性
3. **UI交互测试**: 验证班级切换和列表更新
4. **异常处理测试**: 验证网络异常和数据异常的处理

## 7. 部署和使用

### 7.1 文件清单
- `src/TUIKit/services/addressBookService.js` - 通讯录服务
- `src/TUIKit/components/TUIContact/contact-list/index.vue` - 联系人列表组件
- `src/TUIKit/interface.ts` - 类型定义扩展
- `src/pages/test-addressbook/index.vue` - 测试页面

### 7.2 使用说明
1. 确保用户已登录并设置了正确的角色信息
2. 组件会自动根据角色获取对应的通讯录数据
3. 用户可以通过班级选择器切换不同班级
4. 学生列表会根据选择的班级动态更新

## 8. 总结

本次改造成功实现了：
1. **业务适配**: 从通用IM好友模式改为学校通讯录模式
2. **角色区分**: 支持家长和老师不同的数据获取方式
3. **功能增强**: 新增班级切换和学生管理功能
4. **兼容性**: 保持与原有TUIKit组件的完全兼容

改造采用最小化修改原则，在不破坏原有架构的基础上，通过服务层和数据适配层实现了业务需求，确保了系统的稳定性和可维护性。
