<template>
    <!-- 卡片详情 cardDetail-->
    <view class="card_detail">
        <NavBar title="卡片详情" :clickLeft="clickLeft" />
        <view class="content">
            <uni-list-item v-for="item in cardForm" :key="item.key" :title="item.name" :clickable="item.showArrow"
                @click="onClick(item.key)">
                <template v-slot:footer>
                    <image v-if="item.key == 'imgPath'" class="slot-image" :src="state.form[item.key]" />
                    <text v-else-if="item.key == 'validityStartDate'" class="slot-text">
                        {{ state.form.validityStartDate }} - {{ state.form.validityEndDate }}
                    </text>
                    <text class="slot-updat-password" v-else-if="item.key == 'updatPassword'">
                        修改
                    </text>
                    <text v-else class="slot-text">
                        {{ state.form[item.key] || "-" }}
                    </text>
                </template>
            </uni-list-item>
        </view>
        <view class="footer">
            <button class="mini-btn" type="primary" plain="true" size="mini" @click="handleTo('recharge')">充值</button>

            <button v-if="state.form.cardStatus == 'normal'" class="mini-btn" type="primary" plain="true" size="mini"
                @click="handleTo('reportLoss')">挂失</button>

            <button v-if="state.form.cardStatus === 'lost'" class="mini-btn" type="primary" plain="true" size="mini"
                @click="handleTo('unLost')">解挂</button>

            <button class="mini-btn" type="primary" plain="true" size="mini" @click="handleTo('record')">记录</button>
        </view>
    </view>
</template>

<script setup>
import NavBar from "../components/navBar.vue"
import useStore from "@/store"
const { useSmartCard } = useStore()
const _returnParams = computed(() => useSmartCard.getSmart.returnParams)
const _returnRoute = computed(() => useSmartCard.getSmart.returnRoute)
const cardForm = [
    { name: "头像", key: "imgPath" },
    { name: "姓名", key: "name" },
    { name: "卡号", key: "cardNo" },
    { name: "余额", key: "balance" },
    { name: "卡片状态", key: "cardStatusName" },
    { name: "卡片有效期", key: "validityStartDate" },
    { name: "发卡日期", key: "activatedTime" },
    { name: "卡片押金", key: "cashPledge" },
    { name: "支付密码", key: "updatPassword", showArrow: true },
]
// const popup = ref(null)

const state = reactive({
    form: {
        schoolName: "",
        badgeUrl: "",
        name: "",
        studentId: "",
        imgPath: "",
        cardNo: "",
        validityStartDate: "",
        validityEndDate: "",
        cashPledge: "",
    },
})
const onClick = (key) => {
    // 修改密码
    if (key == "updatPassword") {
        // 有密码就去修改密码 ，没有就去添加银行卡
        useSmartCard.setReturnParams({
            ..._returnParams.value,
            password: state.form.password,
            loginPhone: state.form.loginPhone
        })
        useSmartCard.setReturnRoute([..._returnRoute.value, '/apps/smartCard/cardDetail/index'])
        uni.navigateTo({
            url: "/apps/smartCard/bankCard/authentication",
            success: (res) => {
                console.log("跳转到设置支付密码页面", res);
            },
            fail: (error) => {
                console.error("跳转失败:", error);
                uni.showToast({
                    title: "页面跳转失败",
                    icon: "none",
                });
            },
        });
    }
}
const handleTo = (routType) => {
    const { name, personId, cardNo } = state.form
    let query = {
        studentName: name,
        cardNo,
        personId
    }
    if (routType == "unLost") {
        // 解挂
        query.type = "unboxing"
        routType = "reportLoss"
    } else if (routType == "recharge") {
        // 充值
        query.personId = _returnParams.value.personId
        query.studentId = _returnParams.value.studentId
    }
    useSmartCard.setReturnParams(query)
    useSmartCard.setReturnRoute([..._returnRoute.value, '/apps/smartCard/cardDetail/index'])
    navigateTo({
        url: `/apps/smartCard/${routType}/index`,
        // query
    })
}
const initPage = () => {
    let params = { id: _returnParams.value.personId }
    http.post("/unicard/app/card/info", params).then(({ data }) => {
        state.form = data
    })
}
onShow(() => {
    initPage()
})


function clickLeft() {
    const _route = _returnRoute.value.pop()
    uni.navigateTo({ url: _route })
}
</script>

<style lang="scss" scoped>
.card_detail {
    background: $uni-bg-color-grey;
    height: 100vh;

    .content {
        background: $uni-bg-color;

        .slot-image {
            width: 80rpx;
            height: 80rpx;
            border-radius: 50%;
        }

        .slot-text {
            font-weight: 400;
            font-size: 28rpx;
            color: #999999;
        }

        .slot-updat-password {
            font-weight: 400;
            font-size: 28rpx;
            color: #00B781;
        }

        :deep(.uni-icons) {
            padding-left: 0;
        }

        :deep(.uni-list-item__container) {
            align-items: center;
        }
    }

    :deep(.container--right) {
        align-items: center;
    }

    .footer {
        padding: 20px 32rpx;
        background: $uni-bg-color;
        display: flex;
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;

        .mini-btn {
            border-color: var(--primary-color);
            color: var(--primary-color);
            flex: 1;
            margin: 0 10rpx;
            padding: 10rpx 0;
        }
    }
}
</style>
