<template>
    <view class='handl_person'>
        <view v-show="isSelectMember">
            <NavBar title="提交" :clickLeft="clickLeft" />
            <view class="content">
                <uni-forms ref="baseForm" :modelValue="state.baseForm">
                    <view class="forms_item">
                        <uni-forms-item>
                            <template #label>
                                <view class="label">下一步操作</view>
                            </template>
                            <uni-easyinput class="input" :inputBorder="false" v-model="state.baseForm.nextFlowTitle"
                                disabled placeholder="请输入" />
                        </uni-forms-item>
                    </view>

                    <view class="forms_item">
                        <uni-forms-item required>
                            <template #label>
                                <view class="label">
                                    <text class="required">*</text>办理人员
                                </view>
                            </template>
                            <view v-if="state.nextUserList.length" class="rest_placeholder active">
                                <text>{{ state.baseForm.treeSubmitListName }}</text>
                            </view>
                            <view v-else @click="handleTeacher" class="rest_placeholder">
                                <text class="text" v-if="!state.baseForm.treeSubmitListName">请输入</text>
                                <text v-else>{{ state.baseForm.treeSubmitListName }}</text>
                            </view>
                        </uni-forms-item>
                    </view>
                    <view class="forms_item opinion">
                        <uni-forms-item>
                            <template #label>
                                <view class="label">意见输入</view>
                            </template>
                            <uni-easyinput class="input" autoHeight :inputBorder="false" type="textarea"
                                :maxlength="maxlength" v-model="state.baseForm.comment" placeholder="请输入" />
                            <view class="label textarea_count">{{ state.baseForm.comment.length }}/{{ maxlength }}
                            </view>
                        </uni-forms-item>
                    </view>
                </uni-forms>
            </view>
            <view class="footer">
                <button class="mini-btn" type="default" size="mini" @click="handleCancel">取 消</button>
                <button class="mini-btn" type="primary" size="mini" :loading="state.submitLoading"
                    @click="handleSubmit">
                    确 定
                </button>
            </view>
        </view>
        <selectMember v-show="!isSelectMember" @selectMember="handerSelectMember" v-model:isSelectMember="isSelectMember"
            :treeSubmitList="treeSubmitList" />
    </view>
</template>

<script setup>
import { onMounted } from "vue"
import NavBar from "../../components/navBar.vue"
import selectMember from "../../selectMember/index.vue"
import useStore from "@/store"
const { officialDocs } = useStore()
const _formListData = computed(() => officialDocs.getFormListData)
const _getDocsParams = computed(() => officialDocs.getDocsParams)


const maxlength = 200

const state = reactive({
    id: '',
    previewUrls: [],
    submitLoading: false,
    baseForm: {
        resultMap: '',
        nextFlowTitle: "",
        nodeCode: '',
        treeSubmitListName: '',
        comment: "",
        // 同意操作
        assigneeIds: [],
        nodeId: '',
        documentId: _getDocsParams.value.documentId
    },
})
// 选人
const isSelectMember = ref(true)
const treeSubmitList = ref([])
const handleTeacher = () => {
    isSelectMember.value = false
}

const handerSelectMember = (data) => {
    treeSubmitList.value = data.treeSubmitList
    state.baseForm.treeSubmitListName = data.treeSubmitListName
    state.baseForm.assigneeIds = data.treeSubmitList.map(v => v.id)
    isSelectMember.value = true
}
// 选人
// state.baseForm.resultMap = computed(() => {
//     const obj = {
//         "CHUAN_YUE": "传阅",
//         "YUE_DU": "阅读",
//         "BAN_JIE": "办结",
//         "GUI_DANG": "归档",
//         "CHENG_BAN": "承办",
//         "QIAN_ZHANG": "签章",
//         "FEN_FA": "分发",
//         "BAN_JIE": "办结",
//     }
//     return obj[_formListData.value.status] || '同意'
// })

const getNextFlowElement = async () => {
    try {
        if (!_getDocsParams.value?.documentId) {
            return
        }
        const { data } = await http.post("/cloud/official-doc/workflow/getNextFlowElement", { documentId: _getDocsParams.value.documentId })
        state.baseForm.nextFlowTitle = data.name
        state.baseForm.nodeCode = data.code
        state.baseForm.nodeId = data.id
        state.nextUserList = data.userInfoList
        state.baseForm.assigneeIds = []
        state.baseForm.treeSubmitListName = ''
        if (data.userInfoList.length) {
            let _treeSubmitListName = []
            data.userInfoList.forEach(item => {
                _treeSubmitListName.push(item.name)
                state.baseForm.assigneeIds.push(item.id)
            })
            state.baseForm.treeSubmitListName = _treeSubmitListName.join("、")
        }

    } catch (error) { }
}

// 取消
const handleCancel = () => {
    clickLeft()
}
const handleSubmit = async () => {
    state.submitLoading = true
    try {
        let parasm = {
            ...state.baseForm
        }
        let API = "/cloud/official-doc/workflow/startProcInstance"
        if (_formListData.value.status == "NI_GAO") {
            API = '/cloud/official-doc/workflow/completeTask'
            parasm.nextNodeId = parasm.nodeId
            parasm.outcome = 2
            parasm.taskId = _formListData.value.taskId
        }
        const res = await http.post(API, parasm)
        state.submitLoading = false
        uni.showToast({
            title: res.message,
        })
        if (_getDocsParams.value.status == 'NI_GAO') {
            navigateTo({
                url: "/apps/officialDocs/manage/index",
            })
        } else {
            navigateTo({
                url: '/apps/officialDocs/manage/saveDocList/index',
            })
        }
    } catch (error) {
        state.submitLoading = false
    }

}
function clickLeft() {
    uni.navigateBack()
}
onMounted(async () => {
    isSelectMember.value = true
    getNextFlowElement()
})
</script>

<style lang='scss' scoped>
$bag: #F7F7F7;

.handl_person {
    .content {
        background: $uni-text-color-inverse;

        /* #ifdef MP-WEIXIN */
        .uni-forms-item {
            display: block;
        }

        .label {
            font-weight: 400;
            font-size: 24rpx;
            color: #666666;
            margin-bottom: 6rpx;
        }

        /* #endif */

        .forms_item {
            :deep(.uni-forms-item) {
                display: block;
                padding: 0 30rpx;
            }

            &.opinion {
                border-top: 20rpx solid $bag;
                padding: 30rpx;

                :deep(.uni-forms-item) {
                    padding: 0;
                }

                .label {
                    font-size: 28rpx;
                    color: $uni-text-color;
                }

                .input {
                    text-indent: 20rpx;
                }

                .textarea_count {
                    text-align: right;
                    background-color: $bag !important;
                    padding: 0 10rpx 10rpx;

                    font-weight: 400;
                    font-size: 28rpx;
                    color: $uni-text-color-grey;
                }

                :deep(.uni-easyinput__content) {
                    background-color: #F7F7F7 !important;
                    text-indent: 20rpx;
                }
            }

            .label {
                margin-bottom: 10rpx;
                font-weight: 400;
                font-size: 24rpx;
                color: #666666;

                .required {
                    color: red;
                }
            }

            .rest_placeholder {
                background-color: $bag !important;
                padding: 20rpx;

                .text {
                    color: #d5d1d1;
                    font-size: 24rpx;
                }
            }

            .input {

                :deep(.uni-easyinput__content-input),
                :deep(.uni-easyinput__content) {
                    background-color: $bag !important;

                }
            }
        }
    }

    .footer {
        position: fixed;
        left: 0;
        bottom: 0;
        box-sizing: border-box;
        background: $uni-text-color-inverse;
        width: 100vw;
        padding: 30rpx 30rpx 68rpx;
        text-align: right;

        .mini-btn {
            margin: 0 10rpx;
            padding-top: 6rpx;
            padding-bottom: 6rpx;
            border: 1px solid $uni-text-color-grey;
            background-color: $uni-text-color-inverse;

            &[type="primary"] {
                background-color: $uni-color-primary;
                border: 1px solid $uni-color-primary
            }

        }
    }

}
</style>