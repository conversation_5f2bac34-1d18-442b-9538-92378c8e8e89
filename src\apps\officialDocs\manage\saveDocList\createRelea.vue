<template>
    <view class='create_relea'>
        <NavBar :title="_getDocsParams?.title || ''" :clickLeft="clickLeft" />
        <Tabs :list="tabs" @change="changeTabs" v-model:current="state.review"></Tabs>
        <OfficialDocs v-show="state.review == 'officialDocs'" ref="officialDocsRef" />
        <Content v-show="state.review == 'content'" />
        <Attachment v-show="state.review == 'attachment'" />
        <view class="footer" v>
            <button class="mini-btn" type="primary" size="mini" @click="handleSaveDoc">提交</button>
        </view>
    </view>
</template>

<script setup>
import Tabs from '../../components/tabs.vue'
import NavBar from "../../components/navBar.vue"
import OfficialDocs from "./officialDocs.vue"
import Content from "./content.vue"
import Attachment from './attachment.vue'
import useStore from "@/store"
const { officialDocs } = useStore()
const _getDocsParams = computed(() => officialDocs.getDocsParams)
const _submitFormData = computed(() => officialDocs.submitFormData)
const _getFormListData = computed(() => officialDocs.getFormListData)
const officialDocsRef = ref(null)
const tabs = [
    { name: '稿纸', key: 'officialDocs' },
    { name: '正文', key: 'content' },
    { name: '附件', key: 'attachment' },
]
const state = reactive({
    docsList: [],
    review: 'officialDocs'
})
const changeTabs = (item) => {
    state.review = item.key
}

function clickLeft() {

    if (_getDocsParams.value.status == 'NI_GAO') {
        navigateTo({
            url: "/apps/officialDocs/manage/index",
        })
        return
    }
    navigateTo({
        url: "/apps/officialDocs/manage/saveDocList/index",
    })
}
const initPage = async () => {
    const { id, status } = _getDocsParams.value
    uni.showToast({
        title: '加载中...',
        icon: "loading"
    })
    try {
        uni.showToast({
            title: '加载中...',
            icon: "loading"
        })
        let API = '/cloud/official-doc/process-template/get'
        if (status == 'NI_GAO') {
            API = '/cloud/official-doc/document/get'
        }
        const { data } = await http.get(API, { id })
        state.formListData = data
        officialDocs.setFormListData(data)
        uni.hideToast()
        uni.hideToast()
        state.docsList = data

    } catch (error) {
        uni.hideToast()
    } finally {
        uni.hideToast()
    }
}
const createDoc = async (params) => {
    // const  回退到发起人的时候要这个接口
    let API = "/cloud/official-doc/document/create"
    if (_getDocsParams.value.status == 'NI_GAO') {
        API = '/cloud/official-doc/document/update'
    }
    await http.post(API, params).then(({ data }) => {
        officialDocs.setDocsParams({ ..._getDocsParams.value, documentId: data })
        navigateTo({
            url: '/apps/officialDocs/manage/saveDocList/handlPersonnel',
        })
    })
}
// 发布
const handleSaveDoc = () => {
    officialDocsRef.value.sbumitVerify().then(res => {
        if (res) {
            const { formItems, formJson, type, settings, formProcessTemplateId } = _getFormListData.value
            const params = {
                ..._submitFormData.value,
                type,
                formJson: formItems || formJson,
                formProcessTemplateId: formProcessTemplateId || _submitFormData.value.id,
                settingsValue: settings,
            }
            if (!params.fileId) {
                uni.showModal({
                    content: "当前没有上传正文，确认要提交吗？",
                    cancelText: "取消",
                    confirmText: "确定",
                    confirmColor: "#00B781",
                    cancelColor: "#00B781",
                    success: (res) => {
                        res.confirm && createDoc(params)
                    },
                });
            } else {
                createDoc(params)
            }
        }
    })
}
onMounted(() => {
    initPage()
})

</script>

<style lang='scss' scoped>
.create_relea {
    height: 100vh;
    background: #F6F6F6;

    .footer {
        position: fixed;
        left: 0;
        bottom: 0;
        box-sizing: border-box;
        background: $uni-text-color-inverse;
        width: 100vw;
        padding: 23rpx 30rpx 47rpx;
        text-align: right;

        .mini-btn {
            margin: 0 10rpx;
            padding-top: 6rpx;
            padding-bottom: 6rpx;
            border: 1px solid $uni-text-color-grey;
            background-color: $uni-text-color-inverse;

            &[type="primary"] {
                background-color: $uni-color-primary;
                border: 1px solid $uni-color-primary
            }

        }
    }
}
</style>