<template>
    <!-- 我的订单 my_order 都是已支付的数据-->
    <view class="my_order">
        <z-paging ref="paging" v-model="state.dataList" @query="initPage" :auto="false">
            <template #top>
                <NavBar title="我的订单" :clickLeft="clickLeft" />
            </template>
            <view class="content">
                <uni-section class="rese-section" size="normal" title="订单记录" type="line" padding>
                    <view class="" v-for="item in state.dataList" :key="item.id" @click="amountDetailsItem(item.id)">
                        <List :title="item.title" :time="item.payTime" :money="item.payAmount"
                            :status="item.orderStatus">
                            <template #slot-chapter v-if="[2, 3, 5, 7].includes(item.orderStatus)">
                                <image class="chapter" :src="imgStatus(item)" />
                            </template>
                        </List>
                    </view>
                </uni-section>
            </view>
            <template #empty>
                <yd-empty text="暂无数据" />
            </template>
        </z-paging>
    </view>
</template>

<script setup>
import NavBar from "../components/navBar.vue"
import List from "../components/list.vue"
import useStore from "@/store"
const { useSmartCard } = useStore()
const _returnParams = computed(() => useSmartCard.getSmart.returnParams)
const _returnRoute = computed(() => useSmartCard.getSmart.returnRoute)
// 已支付： orderStatus: 2,3,4,5 退款状态判断： 等于2时，判断是否有部分退款的 ，
// 3,4,5 直接展示： 3.审核中 4.退款中 5.退款失败
// 已关闭：orderStatus  6 ,7  ,6不展示信息， 7展示退款成功
// successStatus 成功   failStatus 失败  processStatus 审核中  3. partialRefund 部分退款
const imgStatus = computed(() => {
    return (item) => {
        const imgStatusMap = {
            2: "partialRefund", // 部分退款
            3: "processStatus", // 审核中
            // 4: 'processStatus',   // 退款中
            5: "failStatus", // 退款失败
            7: "successStatus" // 成功
        }
        if (item.orderStatus == 2) {
            if (item.refundStatus == 3) {
                return `https://file.1d1j.cn/cloud-mobile/smartCard/partialRefund.png`
            }
            return ""
        }
        return `https://file.1d1j.cn/cloud-mobile/smartCard/${imgStatusMap[item.orderStatus]}.png`
    }
})
const paging = ref(null)
const state = reactive({
    dataList: [],
    personId: ""
})
// 查看订单详情
const amountDetailsItem = (id) => {
    useSmartCard.setReturnParams({
        ..._returnParams.value,
        id, personId: state.personId, type: "myOrder"
    })
    useSmartCard.setReturnRoute([..._returnRoute.value, '/apps/smartCard/myOrder/index'])
    navigateTo({
        url: "/apps/smartCard/myOrder/detail",
        // query: { id, personId: state.personId, type: "myOrder" }
    })
}
const initPage = (pageNo, pageSize) => {
    const params = {
        personId: state.personId,
        pageNo,
        pageSize
    }

    http.post("/unicard/app/order/pay-order-page", params)
        .then(({ data }) => {
            paging.value?.complete(data.list || [])
        })
        .catch(() => {
            paging.value?.complete([])
        })
}
onShow(() => {
    paging.value?.reload()
})
onMounted(async () => {
    paging.value?.reload()
})
onLoad(() => {
    state.personId = _returnParams.value.personId || ""
})
function clickLeft() {
    const _route = _returnRoute.value.pop()
    uni.navigateTo({ url: _route })
}
</script>

<style lang="scss" scoped>
.my_order {
    background: linear-gradient(180deg, $uni-bg-color 0%, #f5f7f9 19%, #f2f7f8 100%);
    height: 100vh;

    .logo-school {
        display: flex;
        align-items: center;
        flex: 1;

        .logo {
            width: 56rpx;
            height: 56rpx;
            margin-right: 16rpx;
            border-radius: 50%;
        }

        .school {
            font-weight: 500;
            font-size: 36rpx;
            color: #000000;
        }
    }

    .content {
        margin: 20rpx;

        // #ifdef MP-WEIXIN
        :deep(.uni-section) {
            background: #f4f7f8 !important;
        }

        :deep(.line) {
            background-color: $uni-color-primary;
        }

        // #endif
        .rese-section {
            background-color: transparent;

            :deep(.uni-section-content),
            :deep(.uni-section-header) {
                padding: 0 !important;
            }

            :deep(.list) {
                position: relative;

                .chapter {
                    width: 160rpx;
                    height: 130rpx;
                    position: absolute;
                    right: 160rpx;
                    top: 20rpx;
                }
            }

            :deep(.line) {
                background-color: var(--primary-color);
            }
        }
    }
}
</style>
