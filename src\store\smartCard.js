import { defineStore } from "pinia"

const useSmartCard = defineStore("smartCard", {
    state: () => {
        return {
            smart: {
                returnRoute: [], // 返回路由
                returnParams: {
                    crossRoute: "" // 越级跳转
                }, // 返回参数
                isPassword: false // 是否需要密码
            }
        }
    },
    getters: {
        // 获取smart
        getSmart(state) {
            return state.smart
        }
    },
    actions: {
        // 设置返回路由地址
        setReturnRoute(item) {
            this.smart.returnRoute = item
        },
        // 设置返回路由参数
        setReturnParams(item) {
            this.smart.returnParams = item
        },
        setPassword(item) {
            this.smart.isPassword = item
        }
    },
    persist: {
        key: "yd-mobile-smartCard",
        paths: ["smartCard"],
        debug: import.meta.env.VITE_USER_NODE_ENV === "production",
        beforeRestore: (ctx) => {
            console.log(`beforeRestore '${ctx.store.$id}'`)
        },
        afterRestore: (ctx) => {
            console.log(`afterRestore '${ctx.store.$id}'`)
        }
    }
})

export default useSmartCard
