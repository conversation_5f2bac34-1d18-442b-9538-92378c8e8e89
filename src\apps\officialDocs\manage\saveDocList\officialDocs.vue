<template>
    <view class="official_docs" :class="{ active: !_formListData.taskId }">
        <view class="content">
            <view class="content-form">
                <uni-forms class="yd-form" ref="baseFormRef" :modelValue="state.formListData" :label-width="100"
                    :rules="rules">
                    <view class="form-item" v-for="item in formList" :key="item.name" :class="item.fieldType">
                        <uni-forms-item :label="item.label" :required="item.required" :name="item.fieldName"
                            @click="handleClick(item)">
                            
                            <view class="reset-input" v-if="['input', 'select'].includes(item.fieldType)">
                                <uni-easyinput :clearable="item.fieldType === 'select'" :inputBorder="false"
                                    v-model="state.formListData[item.fieldType === 'select' ? item.fieldName + 'Name' : item.fieldName]"
                                    :placeholder="item.placeholder" :maxlength="item.maxlength"
                                    :disabled="_disabled(item)" @blur="handleBlur($event, item)" />
                            </view>
                            <view v-else-if="item.fieldType == 'textarea'" class="reset-input">
                                <uni-easyinput type="textarea" autoHeight v-model="state.formListData[item.fieldName]"
                                    :placeholder="item.placeholder" :maxlength="item.maxlength"
                                    @blur="handleBlur($event, item)" />

                                <view class="text-count">
                                    <text>
                                        {{ state.formListData[item.fieldName]?.length || 0 }}/{{ item.maxlength }}
                                    </text>
                                </view>
                            </view>

                            <uni-datetime-picker v-else-if="item.fieldType == 'date'" :border="false" type="date"
                                :clear-icon="false" :placeholder="item.placeholder"
                                v-model="state.formListData[item.fieldName]" @change="handleBlur($event, item)" />
                        </uni-forms-item>
                        <view class="right-icon" :class="{ active: item.fieldType == 'select' }"
                            @click="handleClick(item)">
                            <uni-icons v-if="['select', 'date'].includes(item.fieldType)" type="right" size="16"
                                color="#999999"></uni-icons>
                        </view>
                    </view>

                </uni-forms>
            </view>
        </view>
        <yd-empty class="empty" v-if="!state.loading && !formList.length">暂无数据</yd-empty>

        <uni-popup ref="childPopup" type="bottom" background-color="#fff" borderRadius="20rpx 20rpx 0 0">
            <view class="child-switch">
                <view class="handle">
                    <text class="title">{{ state.popupTitle }}</text>
                    <uni-icons class="close" type="closeempty" size="16" @click="childPopup.close()"></uni-icons>
                </view>
                <view class="child-list">
                    <radio-group @change="radioChange">
                        <label class="uni-list-cell" v-for="item in childList" :key="item.value">
                            <view>{{ item.label }}</view>
                            <view>
                                <radio :value="item.value" :checked="item.value === state.personId" color="#00b781" />
                            </view>
                        </label>
                    </radio-group>
                </view>
            </view>
        </uni-popup>
    </view>
</template>

<script setup>
import { computed, watch } from 'vue'
import useStore from "@/store"
const { officialDocs } = useStore()
const _formListData = computed(() => officialDocs.getFormListData)

const baseFormRef = ref(null)
const rules = ref({})
const childList = ref([])
const objOption = ref({
    urgencyLevel: [
        { value: 'TJ', label: '特急' },
        { value: 'JJ', label: '急件' },
        { value: 'PJ', label: '平件' },
    ],
    securityClassification: [
        { value: 'secret', label: '绝密' },
        { value: 'highSecret', label: '机密' },
        { value: 'confidential', label: '秘密' },
        { value: 'ordinary', label: '普通' },
    ],
    fileType: [],

})
const formList = ref([])
const childPopup = ref(null)
const state = reactive({
    reviewDraftId: '',
    popupTitle: '',
    formListData: {},
    radioType: '',
    personId: '',
    enabledList: [],  // 文件类型
    loading: false
})

state.formListData = computed(() => officialDocs.submitFormData)
// 文件类型
async function enabledList() {
    const { data } = await http.get("/cloud/official-doc/file-classify/enabledList", { classifyType: _formListData.value.type })
    objOption.value.fileType = data.map(v => ({ label: v.classifyName, value: v.id }))
    uni.hideToast()
    state.loading = false
}

// 类型切换
const radioChange = async (evt) => {
    const item = objOption.value[state.radioType].find(v => v.value == evt.detail.value)?.label || ''
    officialDocs.setSubmitFormListKey(state.radioType, evt.detail.value)
    officialDocs.setSubmitFormListKey(`${state.radioType}Name`, item)
    childPopup.value.close()
}
// 开启下拉选项 赋值
const handleClick = (item) => {
    const { fieldType, label, fieldName } = item
    if (['select'].includes(fieldType)) {
        state.radioType = fieldName
        state.popupTitle = label
        childList.value = objOption.value[fieldName]
        // 回显弹框下拉数据
        state.personId = state.formListData[fieldName]
        childPopup.value.open()
    }
}
// input 修改
const handleBlur = (evt, item) => {
    const { fieldType, fieldName } = item
    if (fieldType == "date") {
        officialDocs.setSubmitFormListKey(fieldName, evt)
        return
    }
    if (['input', 'textarea'].includes(fieldType)) {
        officialDocs.setSubmitFormListKey(fieldName, evt.detail.value)
    }
}
const _disabled = computed(() => {
    return (item) => {
        // 可编辑
        return item.fieldType == 'select'
    }
})
watch(() => _formListData.value, val => {
    uni.showToast({
        title: '加载中...',
        icon: "loading"
    })
    state.loading = true
    if (val.settings) {
        formList.value = []
        const _settings = JSON.parse(val.settings)
        if (_settings.cells) {
            ['nodeFileUrl', 'nodeFileId', 'fileId', 'fileUrl', 'taskId', 'id', 'status', 'formJson'].forEach(item => {
                officialDocs.setSubmitFormListKey(item, state.formListData[item] || val[item])
            })
            const cellsValue = Object.values(_settings.cells)
            cellsValue.forEach(item => {
                if (item.fieldInfo) {
                    if (item.fieldInfo.source == "system") {
                        officialDocs.setSubmitFormListKey(item.fieldInfo.fieldName, state.formListData[item.fieldInfo.fieldName] || val[item.fieldInfo.fieldName])
                        // 密级
                        if (item.fieldInfo.fieldName === "securityClassification") {
                            officialDocs.setSubmitFormListKey(`${item.fieldInfo.fieldName}Name`, val['securityClassificationCode'])
                        }
                        // 紧急程度
                        if (item.fieldInfo.fieldName === "urgencyLevel") {
                            officialDocs.setSubmitFormListKey(`${item.fieldInfo.fieldName}Name`, val['urgencyLevelCode'])
                        }
                        // 文件类型
                        if (item.fieldInfo.fieldName === "fileType") {
                            officialDocs.setSubmitFormListKey(`${item.fieldInfo.fieldName}Name`, val['fileTypeCode'])
                        }

                    }
                    if (item.fieldInfo.required) {
                        rules.value[item.fieldInfo.fieldName] = { errorMessage: `${item.fieldInfo.placeholder}${item.fieldInfo.label}` }
                    }
                    formList.value.push({
                        ...item.fieldInfo,
                    })
                }
            })
        }
        enabledList()
    }
}, { immediate: true })
const sbumitVerify = () => {
    return new Promise((resolve, reject) => {
        const isbol = Object.keys(rules.value).every(key => {
            if (!state.formListData[key]) {
                uni.showToast({
                    title: rules.value[key].errorMessage,
                    icon: "none"
                })
                return false
            }
            return true
        })
        resolve(isbol)
    })

}
defineExpose({
    sbumitVerify
})
</script>

<style lang='scss' scoped>
.official_docs {
    overflow: hidden auto;
    height: calc(100vh - 360rpx);

    &.active {
        height: calc(100vh - 190rpx);
    }

    .content {
        margin: 20rpx 0;

        .content-form {
            background-color: $uni-text-color-inverse;
            padding: 0 30rpx;

            .yd-form {

                .form-item {
                    position: relative;
                    border-bottom: 1rpx solid $uni-border-color;

                    // :deep(.uni-forms-item) {
                    margin: 0;
                    padding: 16rpx 0rpx;

                    .reset-input {
                        :deep(.uni-input-input) {
                            color: #999999 !important;
                        }
                    }

                    .uni-forms-item {
                        margin: 0;

                        .uni-forms-item__label {
                            color: #333;
                        }

                        .uni-easyinput__content,
                        .uni-forms-item__content {
                            text-align: right;

                            .uni-icons {
                                display: none !important;
                            }

                            .uni-easyinput__content-input {
                                color: #999 !important;
                            }
                        }



                        .uni-easyinput {
                            text-align: right !important;
                        }

                    }

                    &.date,
                    &.select {
                        padding-right: 30rpx;
                    }

                    &.textarea {
                        position: relative;

                        :deep(.uni-forms-item) {
                            flex: 1;
                            flex-direction: column;
                        }

                        :deep(.is-textarea) {
                            background-color: #F6F6F6 !important;
                        }

                        :deep(.uni-easyinput) {
                            text-align: left !important;
                        }

                        .text-count {
                            position: absolute;
                            bottom: 20rpx;
                            right: 40rpx;
                            color: #999;
                        }

                    }

                    &.date {
                        :deep(.uni-date-x) {
                            justify-content: right;

                            .uni-date__x-input {
                                flex: none !important;
                            }
                        }
                    }

                    .right-icon {
                        position: absolute;
                        right: -4rpx;
                        top: 26rpx;
                        color: #999 !important;
                    }

                    .right-icon.active {
                        width: 200rpx !important;
                        text-align: right !important;
                    }
                }

            }

        }

        :deep(.uni-list-item-hover) {
            background-color: #fff !important;
        }

        :deep(.uni-list-item__container) {
            align-items: center;

            .uni-easyinput {
                .uni-easyinput__content-input {
                    text-align: right;
                    padding: 0 !important;
                }
            }
        }


        .slot-text {
            color: $uni-text-color-grey !important;
            font-weight: 400;
            font-size: 28rpx;



            :deep(.uni-easyinput__content-input) {
                color: $uni-text-color-grey !important;
            }
        }
    }

    .child-switch {
        min-height: 400rpx;

        .handle {
            text-align: center;
            position: relative;
            height: 80rpx;
            line-height: 80rpx;

            .close {
                position: absolute;
                right: 20rpx;
                top: 0;
            }
        }

        .child-list {
            .uni-list-cell {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 20rpx;
                border-bottom: 1rpx solid #f2f2f2;

                :deep(.uni-radio-input) {
                    border: none;
                    background-color: transparent;
                }
            }
        }
    }
}
</style>