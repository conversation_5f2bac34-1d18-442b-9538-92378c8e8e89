<template>
    <view class="selectMemberPage">
        <!-- 头部 -->

        <NavBar title="选择成员" :clickLeft="pageRouterBack" />

        <view v-if="state.isSearchPage">
            <!-- 搜索区域 -->
            <view class="searchBox">
                <uv-search @change="searchItem" @clear="clearItem" :focus="true" :placeholder="complaceholder"
                    :showAction="false" v-model="state.searchKey"></uv-search>
            </view>
            <!-- 接口返回的列表区域 -->
            <scroll-view class="scroll-searchY" :scroll-y="true" :scrollTop="state.scrollTop" :show-scrollbar="false"
                :lower-threshold="50" :scroll-with-animation="true">
                <view>
                    <view class="reqListBox" v-if="state.searchreqListArr && state.searchreqListArr.length">
                        <view class="itemSelect" v-for="item in state.searchreqListArr" :key="item.id">
                            <view class="itemSelect_left" @click="searchselectItemThis(item)">
                                <view class="selectImgBox">
                                    <image v-if="searchisSelected(item)" class="selectImg"
                                        src="@nginx/workbench/evalActivity/teacher/selectYes.png"></image>
                                    <image v-if="!searchisSelected(item)" class="selectImg"
                                        src="@nginx/workbench/evalActivity/teacher/selectNo.png"></image>
                                </view>
                                <view class="avatarBox">
                                    <uv-avatar :text="item.name?.charAt(0)" size="30" fontSize="14"
                                        bg-color="var(--primary-color)"></uv-avatar>
                                </view>
                                <view class="selectName">{{ item.name }}</view>
                            </view>
                        </view>
                    </view>
                    <yd-empty v-else :isMargin="true" text="暂无数据" />
                </view>
            </scroll-view>
            <view class="footBoxOut">
                <view class="footBox" style="justify-content: end">
                    <view class="subBox" @click="savasearch">
                        <view class="subBtn">确定</view>
                    </view>
                </view>
            </view>
        </view>
        <view v-else>
            <!-- 选择人员类型 Subsection 分段器 -->
            <view class="subsectionBox">
                <uv-subsection activeColor="#FFFFFF" inactiveColor="#666666" fontSize="14" bgColor="#F0F2F5"
                    :list="selectMember.subsectionList" keyName="name"
                    custom-item-style="border-radius: 30rpx;backgroundColor: var(--primary-color)"
                    custom-style="height: 64rpx;border-radius: 30rpx;" :current="state.current"
                    @change="subsectionChange"></uv-subsection>
            </view>
            <view class="lineDivide"></view>
            <!-- 搜索区域 -->
            <view class="searchBox">
                <uv-search @click="showsearch" :disabled="true" :placeholder="complaceholder" :showAction="false"
                    v-model="state.keyword"></uv-search>
            </view>
            <!-- 面包屑 可以滑动 -->
            <view class="breadcrumbBox">
                <view class="breadcrumb">
                    <view class="breadcrumb-item" @click="selectBreadAll">
                        <view class="breadcrumb-item-slot">{{ "全部" }}</view>
                        <text class="breadcrumb-item-separator">></text>
                    </view>
                    <view class="breadcrumb-item" v-for="(item, index) in state.bread" :key="item.id"
                        @click="selectBread(item, index)">
                        <view class="breadcrumb-item-slot">{{ item.name }}</view>
                        <text class="breadcrumb-item-separator">></text>
                    </view>
                </view>
            </view>

            <!-- 接口里获取到的数据列表 -->
            <scroll-view class="scroll-Y" :scroll-y="true" :scrollTop="state.scrollTop" :show-scrollbar="false"
                :lower-threshold="50" :scroll-with-animation="true">
                <view class="peopleList">
                    <!-- 全选 -->
                    <view @click="allSelectPeople" class="allSelectBox"
                        v-if="state.reqListArr && state.reqListArr.length">
                        <view class="allSelect">
                            <view class="selectImgBox">
                                <image v-show="state.selectPeople" class="selectImg"
                                    src="@nginx/workbench/evalActivity/teacher/selectYes.png"></image>
                                <image v-show="!state.selectPeople" class="selectImg"
                                    src="@nginx/workbench/evalActivity/teacher/selectNo.png"></image>
                            </view>
                            <view class="selectName">全选</view>
                        </view>
                    </view>

                    <view class="reqListBox" v-if="state.reqListArr && state.reqListArr.length">
                        <view class="itemSelect" v-for="item in state.reqListArr" :key="item.id">
                            <view v-if="item.elterns" class="itemSelect_left elterns" @click="selectItemThis(item)">
                                <view class="selectImgBox">
                                    <image v-if="isSelected(item)" class="selectImg"
                                        src="@nginx/workbench/evalActivity/teacher/selectYes.png"></image>
                                    <image v-if="!isSelected(item)" class="selectImg"
                                        src="@nginx/workbench/evalActivity/teacher/selectNo.png"></image>
                                </view>
                                <view>
                                    <view class="selectName">{{ item.name }}</view>
                                    <view class="selectName" v-for="it in item.elterns" :key="it.id">
                                        {{ it.name }}
                                        <text class="relations-style">{{ relationsType[it.relations] }}</text>
                                    </view>
                                </view>
                            </view>
                            <view v-else class="itemSelect_left" @click="selectItemThis(item)">
                                <view class="selectImgBox"
                                    v-if="selectMember.treeSelectType == 'all' || selectMember.treeSelectType == item.typeValue">
                                    <image v-if="isSelected(item)" class="selectImg"
                                        src="@nginx/workbench/evalActivity/teacher/selectYes.png"></image>
                                    <image v-if="!isSelected(item)" class="selectImg"
                                        src="@nginx/workbench/evalActivity/teacher/selectNo.png"></image>
                                </view>
                                <view class="avatarBox"
                                    v-if="item.typeValue === 'people_dept' || item.typeValue === 'student'">
                                    <uv-avatar :text="item.name?.charAt(0)" size="30" fontSize="14"
                                        bg-color="var(--primary-color)"></uv-avatar>
                                </view>
                                <view class="selectName">{{ item.name }}</view>
                            </view>
                            <view class="itemSelect_right" v-if="item.isSub" @click="nextLevel(item)">
                                <view class="nextImgBox">
                                    <image class="nextImg" src="@nginx/workbench/evalActivity/teacher/nextIcon.png">
                                    </image>
                                </view>
                                <view class="nextName"
                                    :style="[isSelected(item) ? { color: '#999999' } : { color: 'var(--primary-color)' }]">
                                    下级 </view>
                            </view>
                        </view>
                    </view>
                    <yd-empty v-if="!state.reqListArr.length" :isMargin="true" text="暂无数据" />
                </view>
            </scroll-view>

            <view class="footBoxOut">
                <view class="footBox">
                    <view @click="openHasSelectPopup" class="hasSelect">
                        {{ selectedDepartmentPeople }}
                    </view>
                    <!-- <view @click="openHasSelectPopup" class="hasSelect"> 已选：其中有{{ state.selected.length }}个指导老师</view> -->
                    <view class="subBox">
                        <view class="upImg" @click="openHasSelectPopup">
                            <uv-icon size="20" color="var(--primary-color)" name="arrow-up"></uv-icon>
                        </view>
                        <view class="subBtn" @click="outputPeople">确 定</view>
                    </view>
                </view>
            </view>
        </view>
        <view>
            <uv-popup ref="hasSelectPopup" mode="bottom" round="20" custom-style="height: 1000rpx;" :closeable="true">
                <view>
                    <view class="titleBox"> {{ selectedDepartmentPeople }} </view>
                    <view class="hasListBox">
                        <view class="hasListItem" v-for="item in state.copySelected" :key="item.id">
                            <view class="item_left">
                                <view class="item_left_avatar"
                                    v-if="item.typeValue === 'people_dept' || item.typeValue === 'student'">
                                    <uv-avatar :text="item.name?.charAt(0)" size="30" fontSize="14"
                                        bg-color="var(--primary-color)"></uv-avatar>
                                </view>
                                <view class="item_left_name">{{ item.name }}</view>
                            </view>

                            <view class="item_right" @click="removeItem(item)">
                                <view class="right_btn"> 移除 </view>
                            </view>
                        </view>
                    </view>

                    <view class="submitFootBox" @click="confirmBtn">
                        <view class="submitFootBtn"> 确定 </view>
                    </view>
                </view>
            </uv-popup>
        </view>
    </view>
</template>
<script setup>
import NavBar from "../components/navBar.vue"
import { onLoad, onShow } from "@dcloudio/uni-app"
import { selectMember } from "./treeSubmitList"
const props = defineProps({
    isSelectMember: {
        type: Boolean,
        default: false
    },
    treeSubmitList: {
        type: Array,
        default: []
    }
})
const emit = defineEmits(["update:isSelectMember"])
const relationsType = {
    1: "父亲",
    2: "母亲",
    3: "爷爷",
    4: "奶奶",
    5: "外公",
    6: "外婆",
    7: "其他"
}
const state = ref({
    searchreqListArr: [],
    isSearchPage: false,
    selectPeople: false,
    current: 0,
    reqListArr: [],
    bread: [],
    selected: [],
    searchselected: [],
    copySelected: [],
    businessType: 21,
    treeType: 2
})
const complaceholder = computed(() => {
    return `请输入要搜索的${selectMember.value.subsectionList[state.value.current].name}`
})
const selectedDepartmentPeople = computed(() => {
    let peoples = []
    let dept = []
    state.value.selected.forEach((v) => {
        if (["student", "people_dept", "eltern"].includes(v.typeValue) || v.hasOwnProperty("elterns")) {
            peoples.push(v)
        } else {
            dept.push(v)
        }
    })
    return `已选：其中有${dept.length}个部门(含子部门)/班级, ${peoples.length}人`
})

const showsearch = () => {
    // 点击搜索清除数据
    state.value.searchreqListArr = []
    state.value.searchselected = []
    state.value.searchKey = ""
    state.value.isSearchPage = true
}

const isSelected = computed(() => {
    return (item) => !!~state.value.selected.findIndex((i) => i.id === item.id)
})

const searchisSelected = computed(() => {
    return (item) => !!~state.value.selected.findIndex((i) => i.id === item.id) || !!~state.value.searchselected.findIndex((i) => i.id === item.id)
})

const hasSelectPopup = ref(null)

const openHasSelectPopup = () => {
    // 每次展开的时候 直接深拷贝一个数据拿过来用用啊
    state.value.copySelected = JSON.parse(JSON.stringify(state.value.selected))
    hasSelectPopup.value.open()
}

// 获取到选人组件列表数据
async function getSelectList(options = {}) {
    const params = {
        treeType: state.value.treeType,
        pid: 0,
        businessType: state.value.businessType,
        code: null,
        isRule: false, // 是否权限校验
        typeValue: null,
        ...options
    }
    let URL = "/cloud/v3/tree/selectTree"
    // 如果是获取家长数据
    if (state.value.businessType == 12 && params.typeValue === "classes") {
        URL = "/app/student/search"
        params.classesId = params.pid
    }
    await http
        .post(URL, params)
        .then(({ data }) => {
            state.value.reqListArr = data
        })
        .finally(() => { })
}

// 搜索
function searchSelectList(options = {}) {
    const params = {
        treeType: state.value.treeType,
        businessType: state.value.businessType,
        code: null,
        isRule: false,
        searchKey: state.value.searchKey,
        pageNo: 1,
        pageSize: 500,
        ...options
    }
    http.post("/cloud/v3/tree/selectTree/search", params).then(({ data }) => {
        state.value.searchreqListArr = data.list
    })
}

const searchItem = (value) => {
    searchSelectList()
}

const clearItem = () => {
    searchSelectList()
}
// 选中这个item
const selectItemThis = (item) => {
    // 如果是全部类型 或者 当前类型与item类型一致 则可以显示选中按钮
    if (selectMember.value.treeSelectType == "all" || selectMember.value.treeSelectType == item.typeValue || item.hasOwnProperty("elterns")) {
        const index = state.value.selected.findIndex((i) => i.id === item.id)
        if (~index) {
            state.value.selected.splice(index, 1)
        } else {
            state.value.selected.push(item)
        }
    }
}

// 搜索选中这个item
const searchselectItemThis = (item) => {
    const index = state.value.searchselected.findIndex((i) => i.id === item.id)
    if (~index) {
        state.value.searchselected.splice(index, 1)
    } else {
        state.value.searchselected.push(item)
    }
}

const savasearch = () => {
    if (state.value.searchselected && state.value.searchselected.length) {
        state.value.searchselected.forEach((item) => {
            state.value.selected.push(item)
            state.value.isSearchPage = false
        })
    }
}
// 下一步
const nextLevel = async (item) => {
    if (!~state.value.selected.findIndex((i) => i.id === item.id)) {
        // 拼面包屑
        state.value.bread = [
            ...state.value.bread,
            {
                id: item.id,
                name: item.name,
                ...item
            }
        ]
        await getSelectList({ typeValue: item.typeValue, pid: item.id })
        // 存储当前选择类型中的面包屑的数据
        selectMember.value.treeSubmitBread[selectMember.value.treeSelectType] = JSON.parse(JSON.stringify(state.value.bread))

        // state.value.selectPeople = backfill(state.value.selected)
    }
}

// 点面包
const selectBread = (item, index) => {
    state.value.selectPeople = false
    state.value.bread = state.value.bread.slice(0, index + 1)
    getSelectList({ typeValue: item.typeValue, pid: item.id })
}

// 清空
const selectBreadAll = (item, index) => {
    state.value.bread = []
    getSelectList()
}

// 全选
const allSelectPeople = () => {
    state.value.selectPeople = !state.value.selectPeople
    if (state.value.selectPeople) {
        // 如果都需要选 则直接全选
        if (selectMember.value.treeSelectType == "all") {
            // 合并去重
            state.value.selected = [...new Set([...state.value.reqListArr, ...state.value.selected])]
        } else {
            // 如果只选择人员 则根据bread来判断
            let _reqListArr = []
            state.value.reqListArr.forEach((item) => {
                // 指定的人员类型
                if (selectMember.value.treeSelectType == item.typeValue) {
                    _reqListArr.push(item)
                }
                if (item.elterns) {
                    _reqListArr.push(item)
                }
            })
            state.value.selected = [...new Set([..._reqListArr, ...state.value.selected])]
        }
    } else {
        // 取消全选就直接过滤不要的
        state.value.selected = state.value.selected.filter((item1) => !state.value.reqListArr.some((item2) => item2.id === item1.id))
    }
}
// 根据选择的数据 来判断是否全选
const backfill = (val) => {
    let num = 0
    //只判断 当下的 people_dept、eltern 是否全部选中
    let people_dept_num = 0
    let eltern_num = 0
    if (["people_dept", "eltern"].includes(selectMember.value.treeSelectType)) {
        // 先处理选中的 people_dept、eltern 的数据
        val.forEach((item) => {
            if (item.typeValue == "people_dept") {
                people_dept_num++
            }
            // 判断是否存在elterns字段
            if (item.typeValue == "eltern" || item.hasOwnProperty("elterns")) {
                eltern_num++
            }
        })
        // 循环判断 当前的people_dept、eltern 的数据是否和reqListArr 一样
        state.value.reqListArr.forEach((item) => {
            if (selectMember.value.treeSelectType == item.typeValue || item.hasOwnProperty("elterns")) {
                num++
            }
        })

        if (selectMember.value.treeSelectType == "people_dept" && people_dept_num) {
            return people_dept_num === num
        }
        if (selectMember.value.treeSelectType == "eltern" && eltern_num) {
            return eltern_num === num
        }
        return false
    }
    return state.value.reqListArr.every((item1) => val.some((item2) => item2.id === item1.id))
}

// 移除
const removeItem = (item) => {
    state.value.copySelected = state.value.copySelected.filter((copyitem) => copyitem.id !== item.id)
}
// 将移除的数据 赋值给选中的数据  然后关闭弹窗
const confirmBtn = () => {
    state.value.selected = JSON.parse(JSON.stringify(state.value.copySelected))
    hasSelectPopup.value.close()
}

// 切换成员类型
const subsectionChange = (index) => {
    state.value.selectPeople = false
    state.value.current = index
    state.value.businessType = selectMember.value.subsectionList[index].businessType
    state.value.treeType = selectMember.value.subsectionList[index].treeType
    state.value.bread = []
    // 切换成员类型时，判断是否选人（选人类型）
    selectMember.value.treeSelectType = selectMember.value.subsectionList[index].treeSelectType

    getSelectList({
        typeValue: null,
        pid: 0
    })
}

const pageRouterBack = () => {
    if (state.value.isSearchPage) {
        state.value.isSearchPage = false
    } else {
        emit("update:isSelectMember", true)
    }
    selectMember.value.treeSubmitList = []
}

const outputPeople = () => {
    selectMember.value.treeSubmitList = JSON.parse(JSON.stringify(state.value.selected))
    selectMember.value.treeSubmitListName = state.value.selected.map((item) => item.name).join("、")
    emit("selectMember", {
        treeSubmitList: selectMember.value.treeSubmitList || [],
        treeSubmitListName: selectMember.value.treeSubmitListName,
        bread: selectMember.value.treeSubmitBread
    })
    emit("update:isSelectMember", true)
    pageRouterBack()
}

watch(
    () => state.value.selected,
    (val) => {
        if (val?.length) {
            state.value.selectPeople = backfill(val)
        } else {
            state.value.selectPeople = false
        }
    },
    {
        deep: true,
        immediate: true
    }
)
// 可以多次触发
onShow(() => {
    state.value.selected = props.treeSubmitList
})
onMounted(() => { 
    const _subsectionList = [{ name: "教职工组", businessType: 21, treeType: 2, treeSelectType: "people_dept" }]
    // const _subsectionList = item && item.subsectionList && JSON.parse(item.subsectionList)
    if (_subsectionList) {
        selectMember.value.subsectionList = _subsectionList
        _subsectionList.forEach((v) => {
            if (state.value.treeType == v.treeType) {
                state.value.businessType = v.businessType
                selectMember.value.treeSelectType = v.treeSelectType || "all"
                // 拿到面包屑的数据
                selectMember.value.treeSubmitBread[selectMember.value.treeSelectType] = []
            }
        })
    }
    getSelectList()
})

</script>
<style lang="scss" scoped>
.selectMemberPage {
    height: 100vh;
    overflow: hidden;
}

.title_box {
    width: 100%;
    text-align: center;
    line-height: 88rpx;
    font-size: 34rpx;
    font-weight: 500;
    color: $uni-text-color;
}

.subsectionBox {
    padding: 30rpx;
}

.lineDivide {
    background: $uni-bg-color-grey;
    height: 20rpx;
}

.searchBox {
    padding: 30rpx;
}

.breadcrumbBox {
    overflow-x: scroll;
    font-size: 28rpx;
    padding: 0rpx 30rpx 30rpx;

    .breadcrumb {
        display: flex;
    }
}

.breadcrumb-item {
    display: flex;
    align-items: center;
    white-space: nowrap;
}

.breadcrumb-item-slot {
    padding: 0 10px;
    color: $uni-text-color-grey;
}

.breadcrumb-item:first-child .breadcrumb-item-slot {
    padding-left: 0;
}

.breadcrumb-item:last-child>.breadcrumb-item-separator {
    display: none;
}

.breadcrumb-item-separator {
    color: #cccccc;
}

.peopleList {
    font-size: 28rpx;
}

.allSelectBox {
    border-bottom: 1px solid $uni-border-color;

    .allSelect {
        display: flex;
        align-items: center;
        padding: 0rpx 30rpx 30rpx;

        .selectImgBox {
            width: 36rpx;
            height: 36rpx;

            .selectImg {
                width: 36rpx;
                height: 36rpx;
            }
        }

        .selectName {
            padding-left: 20rpx;
            font-weight: 400;
            font-size: 28rpx;
            color: $uni-text-color;

            text-align: left;
            font-style: normal;
        }
    }
}

.reqListBox {
    padding-left: 30rpx;
    padding-right: 30rpx;

    .itemSelect {
        padding: 30rpx 0rpx;
        display: flex;
        border-bottom: 1px solid $uni-border-color;
        align-items: center;
        justify-content: space-between;

        .selectImgBox {
            width: 36rpx;
            height: 36rpx;

            .selectImg {
                width: 36rpx;
                height: 36rpx;
            }
        }

        .avatarBox {
            padding-left: 20rpx;
        }

        .selectName {
            padding-left: 20rpx;

            font-weight: 400;
            font-size: 28rpx;
            color: $uni-text-color;

            text-align: left;
            font-style: normal;
        }
    }

    .itemSelect_left {
        display: flex;
        align-items: center;
        flex: 1;

        &.elterns {
            .relations-style {
                color: var(--primary-color);
                background-color: #00b78136;
                font-size: 16rpx;
                padding: 4rpx 8rpx;
                border-radius: 5rpx;
            }
        }
    }

    .itemSelect_right {
        display: flex;
        flex-shrink: 0;
        align-items: center;
        border-left: 1px solid $uni-border-color;
        padding-left: 30rpx;

        .nextImgBox {
            width: 40rpx;
            height: 40rpx;

            .nextImg {
                width: 40rpx;
                height: 40rpx;
            }
        }
    }
}

.scroll-Y {
    height: calc(100vh - 575rpx);
}

.scroll-searchY {
    height: calc(100vh - 373rpx);
}

.hasListBox {
    padding-left: 30rpx;
    padding-right: 30rpx;
    height: 720rpx;
    overflow: auto;

    .hasListItem {
        display: flex;
        align-items: center;
        justify-content: space-between;
        border-bottom: 1rpx solid $uni-border-color;
        padding: 30rpx 0rpx;

        .item_left {
            display: flex;
            align-items: center;

            .item_left_avatar {
                padding-right: 16rpx;
            }

            .item_left_name {
                font-weight: 400;
                font-size: 28rpx;
                color: $uni-text-color;
            }
        }

        .item_right {
            .right_btn {
                width: 96rpx;
                height: 48rpx;
                background: $uni-bg-color;
                border-radius: 8rpx;
                border: 1rpx solid $uni-border-color;

                font-weight: 400;
                font-size: 26rpx;
                color: $uni-text-color;
                line-height: 48rpx;
                text-align: center;
            }
        }
    }
}

.footBoxOut {
    display: flex;
    align-items: center;
    border-top: 1px solid $uni-border-color;
    position: absolute;
    bottom: 0;
    background: $uni-bg-color;
    width: 100%;
    height: 155rpx;

    .footBox {
        padding-left: 30rpx;
        padding-right: 30rpx;
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: space-between;

        .hasSelect {
            font-size: 24rpx;
            flex: 1;
        }

        .subBox {
            display: flex;
            align-items: center;

            .upImg {
                padding-left: 20rpx;
                padding-right: 20rpx;
            }

            .subBtn {
                color: $uni-bg-color;
                width: 156rpx;
                height: 80rpx;
                background: var(--primary-color);
                font-size: 28rpx;
                border-radius: 10rpx;
                text-align: center;
                line-height: 80rpx;
            }
        }
    }
}

.titleBox {
    padding: 30rpx;

    font-weight: 600;
    font-size: 30rpx;
    color: $uni-text-color;
    line-height: 42rpx;
}

.submitFootBox {
    position: fixed;
    width: 100%;
    bottom: 0;
    border-top: 1px solid $uni-border-color;
    //   height: 120rpx;
    background: $uni-bg-color;

    //   display: flex;
    //   align-items: center;
    //   justify-content: center;
    .submitFootBtn {
        margin: 30rpx;
        height: 92rpx;
        background: var(--primary-color);
        border-radius: 10rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 400;
        font-size: 32rpx;
        color: $uni-bg-color;
    }
}

.not_set {
    height: 40vh;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;

    .image {
        width: 360rpx;
        height: 210rpx;
    }

    .text {
        font-size: 26rpx;
        font-weight: 400;
        color: #8c8c8c;
        line-height: 36rpx;
        padding-top: 30rpx;
    }
}

.breadcrumb .breadcrumb-item:not(:last-child) .breadcrumb-item-slot {
    color: var(--primary-color);
}
</style>
