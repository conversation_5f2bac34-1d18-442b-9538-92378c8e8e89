<template>
    <view class="official_docs_manage">
        <z-paging ref="paging" v-model="state.dataList" @query="initPage" :auto="false">
            <template #top>
                <view class='header'>
                    <Tabs :list="list" _class="_tabs" @change="changeTabs" v-model:current="state.type"></Tabs>
                    <view class="search-easyinput">
                        <uni-easyinput prefixIcon="search" :inputBorder="false" all v-model="state.form.title"
                            placeholder="请输入标题搜索" @input="iconClick">
                        </uni-easyinput>
                    </view>
                </view>
                <Tabs :list="tabs" v-model:current="state.showType" @change="changeDispatcherTabs"></Tabs>
            </template>

            <view class="content">
                <view class="list">
                    <view class="list-item" v-for="item in state.dataList" :key="item.id">
                        <text class="list-item-status">
                            {{ item.statusCode }}
                        </text>
                        <view class="list-item-title">
                            {{ item.title }}
                        </view>
                        <view class="list-item-info">
                            <view class="list-item-info-item" v-for="info in myInfo" :key="info.key">
                                <text>{{ info.name }}：</text>
                                <text>{{ item[info.key] || '-' }}</text>
                            </view>
                        </view>
                        <view class="list-btns">
                            <button v-if="item.isRevoke && state.showType == 1" class="list-item-btn" type="default"
                                size="mini" @click="handerDelete(item)">撤销</button>

                            <button v-if="item.taskId" class="list-item-btn" type="primary" size="mini"
                                @click="handerView(item)">办理</button>

                            <button v-else class="list-item-btn" type="default" size="mini"
                                @click="handerView(item)">查看</button>

                        </view>
                    </view>
                </view>
            </view>
            <template #empty>
                <slot name="empty">
                    <yd-empty text="暂无数据" />
                </slot>
            </template>
            <template #bottom>
                <view class="tabbar">
                    <view class="tabbar_item" @click="changeTabbar">
                        <image class="tabbar_item_icon" mode="aspectFill"
                            src="https://file.1d1j.cn/cloud-mobile/officialDocs/home.png" />
                        <view class="tabbar_item_text">首页</view>
                    </view>
                    <view class="tabbar_item active">
                        <image class="tabbar_item_icon" mode="aspectFill"
                            src="https://file.1d1j.cn/cloud-mobile/officialDocs/official_active.png" />
                        <view class="tabbar_item_text">公文管理</view>
                    </view>
                </view>
            </template>
            <uni-popup ref="deletePopup" border-radius="10px" background-color="#fff" :is-mask-click="false">
                <view class="delete-popup-content">
                    <view class="header">
                        <text class="title"></text>
                        <uni-icons class="close" type="closeempty" size="20" @click="deletePopup.close()"></uni-icons>
                    </view>
                    <view class="body">
                        确定要撤销么？撤销后不可恢复！
                    </view>
                    <view class="footer-btn">
                        <button class="mini-btn" @click="deletePopup.close()" tyle="default" size="mini">取消</button>
                        <button class="mini-btn" type="primary" :loading="state.deleteLoading"
                            @click="handleSaveDeleteOrder" tyle="primary" size="mini">确定</button>
                    </view>
                </view>
            </uni-popup>
        </z-paging>
    </view>
</template>

<script setup>
import Tabs from '../components/tabs.vue'
import useStore from "@/store"
const { officialDocs } = useStore()
const _getDocsParams = computed(() => officialDocs.getDocsParams)
const deletePopup = ref(null)
const list = [
    { name: '发文管理', key: 'docDrafts' },
    { name: '收文管理', key: 'docReceipts' },
    { name: '签报管理', key: 'memoDrafts' }
]
const tabs = [
    { name: '我收到的', key: 2 },
    { name: '我发布的', key: 1 },
]

const myInfo = computed(() => {
    let docNoName = '发文字号'
    let authorName = '拟稿人'
    if (state.type == 'docReceipts') {
        docNoName = '来文字号'
        authorName = '登记人'
    } else if (state.type == 'memoDrafts') {
        docNoName = '签报字号'
        authorName = '签报人'
    }
    return [
        { name: docNoName, key: 'docNo' },
        { name: '文件类型', key: 'fileTypeCode' },
        { name: authorName, key: 'authorName' },
        { name: '机密高度', key: 'securityClassificationCode' },
        { name: '紧急程度', key: 'urgencyLevelCode' },
        { name: '创建时间', key: 'createTime' },
    ]
})

const paging = ref(null)
const state = reactive({
    type: 'docDrafts',
    showType: 2,
    dataList: [],
    form: {
        title: '',
    },
    deleteId: '',
    deleteLoading: false,
})
// 撤销
const handleSaveDeleteOrder = () => {
    state.deleteLoading = true
    http.post("/cloud/official-doc/workflow/deleteInstance", { processInstanceId: state.deleteId }).then(({ message }) => {
        uni.showToast({
            title: message,
            icon: "success"
        })
        deletePopup.value.close()
        paging.value?.reload()
        state.deleteLoading = false

    }).catch(() => {
        state.deleteLoading = false
    })
}
// 撤销
const handerDelete = (item) => {
    state.deleteId = item.procInstId
    deletePopup.value.open()
}
// 查看 办理
const handerView = (item) => {
    if (item.status == 'NI_GAO') {
        const { id } = item
        officialDocs.setDocsParams({ ..._getDocsParams.value, id, status: item.status })
        officialDocs.setSubmitFormClearKey()
        navigateTo({
            url: '/apps/officialDocs/manage/saveDocList/createRelea',
        })
    } else {
        navigateTo({
            url: '/apps/officialDocs/manage/reviewDraft/index',
            query: {
                id: item.id,
                type: state.type,
                showType: state.showType,
                clear: true
            },
        })
        // 跳转前先清空核稿中的数据
    }
}
// 收发签管理切换
const changeTabs = (item) => {
    state.form.type = item.key
    // #ifdef H5-WEIXIN || H5
    document.title = item.name
    // #endif
    paging.value?.reload()
}
// 我收到的 我发布的切换
const changeDispatcherTabs = (item) => {
    paging.value?.reload()
}
// 标题搜索
const iconClick = () => {
    paging.value?.reload()
}

const initPage = (pageNo, pageSize) => {
    const params = {
        pageNo,
        pageSize,
        ...state.form,
        type: state.type,
        showType: state.showType,
    }
    uni.showToast({
        title: '加载中...',
        icon: "loading"
    })
    http.post("/cloud/official-doc/document/page", params).then(({ data }) => {
        paging.value?.complete(data.list || [])
    }).catch(() => {
        paging.value.complete([])
        uni.hideToast()
    }).finally(() => {
        uni.hideToast()
    })
}

const changeTabbar = () => {
    navigateTo({
        url: '/apps/officialDocs/home/<USER>',
    })
}
onMounted(() => {
    paging.value?.reload()
})
onLoad((item) => {
    Object.keys(item).forEach((key) => {
        item[key] = decodeURIComponent(item[key])
    })
    state.type = item.type || 'docDrafts'
    state.showType = item.showType || 2
})
</script>

<style lang='scss' scoped>
.official_docs_manage {
    background: #F6F6F6;
    height: 100vh;

    .header {
        background-color: $uni-text-color-inverse ;
        overflow: hidden;
        // #ifdef MP-WEIXIN
        padding-top: 50px;

        .uni-easyinput {
            margin: 30rpx;
            width: 92vw;
            border-radius: 30rpx;
            overflow: hidden;

            .uni-easyinput__content-input,
            .uni-easyinput__content,
            :deep(.uni-easyinput__content-input),
            :deep(.uni-easyinput__content) {
                background-color: #F0F2F5 !important;
            }
        }

        // #endif

        .search-easyinput {
            width: auto;
            margin: 30rpx;
            border-radius: 30px;
            overflow: hidden;

            .uni-easyinput__content-input,
            .uni-easyinput__content,
            :deep(.uni-easyinput__content-input),
            :deep(.uni-easyinput__content) {
                background-color: #F0F2F5 !important;
            }
        }
    }

    .tabs {
        border-bottom: 1rpx solid #D9D9D9;
        margin-top: 18rpx;
    }

    .content {
        background-color: $uni-text-color-inverse;

        .list {
            overflow: hidden;

            .list-item {
                box-sizing: border-box;
                margin: 20rpx 30rpx;
                padding: 24rpx;
                background-color: #F7F7F7;
                border-radius: 16rpx;
                position: relative;

                .list-item-status {
                    background: $uni-color-primary;
                    border-radius: 0rpx 16rpx 0rpx 25rpx;
                    padding: 12rpx;
                    position: absolute;
                    top: 0;
                    right: 0;
                    font-weight: 400;
                    font-size: 28rpx;
                    color: $uni-text-color-inverse
                }

                .list-item-title {
                    width: calc(100% - 80rpx);
                    font-weight: 400;
                    font-size: 32rpx;
                    color: $uni-text-color;
                    // 字数超出3行，显示省略号
                    overflow: hidden;
                    text-overflow: ellipsis;
                    display: -webkit-box;
                    -webkit-line-clamp: 1;
                    -webkit-box-orient: vertical;
                }

                .list-item-info-item {
                    font-weight: 400;
                    font-size: 28rpx;
                    color: $uni-text-color-grey;
                    margin: 8rpx 0
                }

                .list-btns {
                    display: flex;
                    align-items: center;
                    justify-content: flex-end;

                    .list-item-btn {
                        font-weight: 400;
                        margin: 0;
                        margin-left: 20rpx;
                        border-radius: 50rpx;

                        &[type="default"] {
                            color: #666666;
                            border: 1rpx solid #D9D9D9;
                        }

                        &[type="primary"] {
                            background-color: $uni-text-color-inverse;
                            border: 1rpx solid $uni-color-primary;
                            color: $uni-color-primary;

                        }
                    }
                }
            }
        }
    }

    .delete-popup-content {
        padding: 20rpx 30rpx;
        width: 80vw;

        .header {
            position: relative;
            text-align: center;

            .title {
                font-weight: 500;
                font-size: 34rpx;
                color: #333333;
            }

            .close {
                position: absolute;
                right: 0;
                top: 0;
            }
        }

        .body {
            text-align: center;
            margin: 60rpx 0;

            :deep(.swiper-box) {
                height: 848rpx !important;

                .uni-list-item__extra-text {
                    font-size: 28rpx;
                }

                .uni-list-item__extra {
                    flex: 2;
                }
            }

            :deep(.uni-swiper__dots-box) {
                height: 45rpx;
            }

            :deep(.uni-list-item__extra-text) {
                max-height: 300rpx;
                overflow: hidden auto;
            }
        }

        .footer-btn {
            display: flex;

            .mini-btn {

                &[type="primary"] {
                    background-color: $uni-color-primary;
                }
            }
        }


    }

    .tabbar {
        height: 120rpx;
        background-color: $uni-text-color-inverse;
        display: flex;
        justify-content: space-evenly;
        align-items: center;

        .tabbar_item {
            text-align: center;

            &.active {
                .tabbar_item_text {
                    color: $uni-color-primary;
                }
            }

            .tabbar_item_icon {
                width: 40rpx;
                height: 40rpx;
            }

            .tabbar_item_text {
                font-weight: 500;
                font-size: 20rpx;
                color: $uni-text-color;
            }
        }

    }
}
</style>