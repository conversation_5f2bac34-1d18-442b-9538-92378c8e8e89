<template>
    <view class="drop-down-filtering">
        <view class="reset-select-item">
            <slot name="leftSelect">
                <uni-data-select v-if="state.dataSelectType != 'allReleases'" v-model="state.myType" :localdata="state.myTypeList" @change="changeSelect" :clear="false"></uni-data-select>
            </slot>
        </view>
        <view class="reset-select-item right">
            <uni-data-select v-model="state.dateStatus" :localdata="dateStatusList" @change="changeSelect" :clear="false"></uni-data-select>
        </view>
    </view>
</template>

<script setup>
import dayjs from "dayjs"
const props = defineProps({
    tabKey: {
        type: [String, Number],
        default: ""
    },
    tabType: {
        type: String,
        default: "receive"
    }
})
const emit = defineEmits(["emitChangeSelect"])
const myTypeList = [
    { text: "我收到的", value: "receive" },
    { text: "我发布的", value: "publish" }
]

const dateStatusList = [
    { text: "全部", value: "all" },
    { text: "今日", value: "day" },
    { text: "本周", value: "weeks" },
    { text: "本月", value: "month" },
    { text: "本学期", value: "semester" }
]
const state = reactive({
    myType: "receive",
    dateStatus: "all",
    dataSelectType: "",
    myTypeList: []
})

// 下拉查询 我收到的  周期切换
const changeSelect = async (data) => {
    let startTime = ""
    let endTime = ""
    // else if (data == "all") {
    //     const time = dayjs()
    //     startTime = time.startOf(data).format("YYYY-MM-DD HH:mm:ss")
    //     endTime = time.endOf(data).format("YYYY-MM-DD HH:mm:ss")
    // }
    if (data === "semester") {
        await http.get("/app/semester/current", {}).then((res) => {
            startTime = res.data?.startDate
            endTime = res.data?.endDate
        })
    } else if (data === "day") {
        const time = dayjs()
        startTime = time.startOf(data).format("YYYY-MM-DD HH:mm:ss")
        endTime = time.endOf(data).format("YYYY-MM-DD HH:mm:ss")
    } else if (data === "weeks") {
        startTime = dayjs().startOf("week").format("YYYY-MM-DD HH:mm:ss")
        endTime = dayjs().endOf("week").format("YYYY-MM-DD HH:mm:ss")
    } else if (data === "month") {
        startTime = dayjs().startOf("month").format("YYYY-MM-DD HH:mm:ss")
        endTime = dayjs().endOf("month").format("YYYY-MM-DD HH:mm:ss")
    } else {
        startTime = ""
        endTime = ""
    }
    console.log(["receive", "publish"].includes(data) ? data : state.myType, '["receive", "publish"].includes(data) ? data : state.myType,')

    const params = {
        type: ["receive", "publish"].includes(data) ? data : state.myType,
        startTime,
        endTime
    }
    emit("emitChangeSelect", params)
}
watch(
    () => props.tabKey,
    (val, old) => {
        if (val !== old) {
            state.myType = props.tabType === "all" ? "receive" : props.tabType
            state.dateStatus = "all"
            changeSelect()
        }
    }
)
onShow(() => {
    if (props.tabType === "all") {
        state.myTypeList = myTypeList
        // state.myType = "receive"
    } else {
        state.myTypeList = myTypeList.filter((v) => v.value === props.tabType)
        state.myType = props.tabType
    }
})

onLoad((options) => {
    Object.keys(options).forEach((key) => {
        options[key] = decodeURIComponent(options[key])
    })
    // 获取dataSelectType 如果是'所有发布'则不显示下拉选项
    state.dataSelectType = options.dataSelectType
})
</script>

<style lang="scss" scoped>
.drop-down-filtering {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 20rpx 0;
    padding: 28rpx;
    background-color: $uni-text-color-inverse;
    // box-shadow: 0 0.05333rem 0.32rem rgba(100, 101, 102, 0.12);
    // #ifdef MP-WEIXIN
    padding: 14rpx 28rpx;
    // #endif

    .reset-select-item {
        min-width: 204rpx;

        &.right {
            min-width: 144rpx;
        }

        :deep(.uni-select__input-text) {
            font-size: 28rpx;
            color: #666666;
        }

        :deep(.uni-select) {
            border: none !important;
            height: 20rpx;

            .uni-select__input-text {
                width: auto;
            }

            .uni-icons:before {
                content: "";
                display: block;
                border: 10rpx solid transparent;
                margin-left: 6rpx;
            }

            .uniui-bottom:before {
                border-top: 10rpx solid var(--primary-color);
                border-bottom-width: 1px;
                margin-top: 6rpx;
            }

            .uniui-top:before {
                border-bottom-color: var(--primary-color);
                border-top-width: 1px;
                margin-bottom: 6rpx;
            }
        }
    }
}
</style>
