<template>
    <view class="one_indicator" v-for="(item, index) in ruleList" :key="index">
        <!-- 一级指标标题 -->
        <view class="one_indicator_item">
            <view class="line"></view>
            <text>{{ item.name }}</text>
        </view>
        <view class="second_indicators_list">
            <view class="second_indicators" v-for="j in item.secondIndicators" :key="j.id">
                <!-- 二级指标标题 -->
                <view class="second_title">
                    <text>
                        {{ j.name }}<text v-if="j.indicatorScore?.maxScore">{{ `（${j.indicatorScore?.maxScore}）分` }}</text></text
                    >
                    <uni-icons @click="openInfo(j)" :type="!j.isShow ? 'down' : 'right'" size="16" color="#8C8C8C"></uni-icons>
                </view>
                <!-- 二级指标详情 -->
                <view v-show="!j.isShow" class="second_info">
                    <!-- 二级指标的内容 -->
                    <view class="info_content">
                        {{ j.indicatorScore?.content || "-" }}
                    </view>
                    <slot name="info" :data="j"></slot>
                    <!-- 分值 -->
                    <view class="score_input" v-if="isShowInput">
                        <uni-number-box v-if="isApprove" :step="Number(ruleData?.minRatingStep) || 1" :value="j.indicatorScore.approveScore || j.indicatorScore.totalIndicatorScore" @change="(value) => indicatorScoreChange(value, j)" />
                        <div v-else>
                            <yd-number :data="j" :ruleData="ruleData" :value="j.indicatorScore.score" :step="Number(ruleData?.minRatingStep) || 1" :min="j.indicatorScore?.minScore" :max="j.indicatorScore?.maxScore" @changeScore="(value) => indicatorScoreChange(value, j)" />
                        </div>
                    </view>
                    <!-- 备注 -->
                    <view class="remarks" v-if="isShowRemarks && j.indicatorScore?.evalScoreTypeList?.includes('text')">
                        <slot name="textarea" :data="j" v-if="isTextarea"></slot>
                        <uni-easyinput :key="j.indicatorScore.id" v-else type="textarea" autoHeight v-model="j.indicatorScore.comment" placeholder="请输入文字" :maxlength="200" primaryColor="var(--primary-color)" @change="commentChange(j)">
                            <template #right>
                                <view class="input_count">{{ j.indicatorScore.comment?.length || 0 }}/200</view>
                            </template>
                        </uni-easyinput>
                    </view>
                    <!-- 上传图片/视频 -->
                    <video-image-com :data="j" v-if="isUploadimage" @delectUrl="(type, urlIndex) => delectUrl(type, urlIndex, j)" @upload="() => handleUpload(j)">
                        <text class="upload_tip" v-if="j.indicatorScore?.evalScoreTypeList?.includes('image') || j.indicatorScore?.evalScoreTypeList?.includes('video')">支持最多上传9张图片，1个视频</text>
                    </video-image-com>
                </view>
            </view>
        </view>
    </view>
</template>

<script setup>
import YdNumber from "./ydNumber.vue"
import videoImageCom from "./videoImageCom.vue"

const emit = defineEmits(["changeScore", "changeData"])
const props = defineProps({
    isApprove: {
        type: Boolean,
        default: false
    },
    list: {
        type: Array,
        default: () => {
            return []
        }
    },
    ruleData: {
        type: Object,
        default: () => {
            return {}
        }
    },
    isUploadimage: {
        type: Boolean,
        default: true
    },
    isShowInput: {
        type: Boolean,
        default: true
    },
    isShowRemarks: {
        type: Boolean,
        default: true
    },
    isTextarea: {
        type: Boolean,
        default: false
    }
})

const isTextarea = computed(() => props.isTextarea)
const isApprove = computed(() => props.isApprove)
const ruleData = computed(() => props.ruleData)
const isUploadimage = computed(() => props.isUploadimage)
const ruleList = computed(() => props.list)

// 是否为图片
const isImageType = computed(() => {
    return (url) => {
        if (url) {
            const type = ["jpeg", "jpg", "png", "bmp"]
            const isType = type.some((i) => {
                return url?.toLowerCase().indexOf(i) !== -1
            })
            return isType
        }
        return false
    }
})
// 是否为视频
const isVideoType = computed(() => {
    return (url) => {
        if (url) {
            const type = ["mp4", "mov"]
            const isType = type.some((i) => {
                return url?.toLowerCase().indexOf(i) !== -1
            })
            return isType
        }
        return false
    }
})

function successFile(res, j) {
    const fileSize = res.tempFiles[0]?.size || 1
    // 校验图片大小（5MB = 5242880字节）
    if (fileSize / 1024 / 1024 > 5) {
        uni.showToast({
            icon: "none",
            title: "图片/视频大小超出最大限制（5MB）"
        })
        return
    }
    // #ifdef H5
    const path = res.tempFiles[0].path
    // #endif
    // #ifdef MP-WEIXIN
    const path = res.tempFiles[0].tempFilePath
    // #endif

    http.uploadFile("/file/common/upload", path, { folderType: "app" }).then((url) => {
        if (isVideoType.value(url)) {
            j.indicatorScore.videoPaths = url
        } else {
            j.indicatorScore.imgPathsList.push(url)
        }
        emit("changeData", j, {
            videoPaths: j.indicatorScore.videoPaths,
            imgPaths: j.indicatorScore.imgPathsList?.join(",") || "",
            comment: j.indicatorScore.comment || ""
        })
    })
}

// 上传视频/图片
function handleUpload(j) {
    const num = j.indicatorScore.videoPaths ? 9 : 10
    if (j.indicatorScore.imgPathsList.length >= num) {
        uni.showToast({
            icon: "none",
            title: "最多上传9张图片，1个视频"
        })
        return
    }
    // #ifdef MP-WEIXIN
    wx.chooseMedia({
        count: 1,
        mediaType: ["image", "video"],
        sourceType: ["album", "camera"],
        // extension: [".jpeg", ".jpg", ".png", ".bmp", ".mp4", ".mov"],
        success: (res) => {
            console.log(res, "res")

            successFile(res, j)
        }
    })
    // #endif
    // #ifdef H5
    uni.chooseFile({
        count: 1,
        extension: [".jpeg", ".jpg", ".png", ".bmp", ".mp4", ".mov"],
        sourceType: ["album", "camera"],
        success: (res) => {
            successFile(res, j)
        }
    })
    // #endif
}

function commentChange(j) {
    emit("changeData", j, {
        videoPaths: j.indicatorScore.videoPaths,
        imgPaths: j.indicatorScore.imgPathsList?.join(",") || "",
        comment: j.indicatorScore.comment || ""
    })
}

// 删除上传的图片/视频
function delectUrl(type, urlIndex = 0, j) {
    if (type == "video") {
        j.indicatorScore.videoPaths = ""
    } else {
        j.indicatorScore.imgPathsList?.splice(urlIndex, 1)
    }
    emit("changeData", {
        videoPaths: j.indicatorScore.videoPaths,
        imgPaths: j.indicatorScore.imgPathsList?.join(",") || "",
        comment: j.indicatorScore.comment || ""
    })
}

// 加减分数
function indicatorScoreChange(value, j) {
    emit("changeScore", value, j)
}

// 展开隐藏
function openInfo(j) {
    j.isShow = !j.isShow
}
</script>

<style lang="scss" scoped>
.one_indicator {
    // 一级指标
    .one_indicator_item {
        display: flex;
        align-items: center;
        .line {
            width: 4rpx;
            height: 22rpx;
            background: var(--primary-color);
            border-radius: 3rpx;
            margin-right: 10rpx;
        }
        font-weight: 500;
        font-size: 28rpx;
        color: rgba(0, 0, 0, 0.85);
        line-height: 40rpx;
    }
    // 二级指标
    .second_indicators_list {
        display: flex;
        flex-direction: column;
        padding: 10rpx 0;
        .second_indicators {
            padding: 10rpx 0;
            .second_title {
                display: flex;
                justify-content: space-between;
                align-items: center;
                font-weight: 400;
                font-size: 28rpx;
                color: rgba(0, 0, 0, 0.85);
                line-height: 40rpx;
            }
            .second_info {
                padding-top: 20rpx;
                .info_content {
                    font-weight: 400;
                    font-size: 28rpx;
                    color: #666666;
                    line-height: 36rpx;
                }
                .score_input {
                    margin: 24rpx 0;
                    :deep(.uni-numbox) {
                        padding: 20rpx;
                        border: 1rpx solid #dadada;
                        border-radius: 10rpx;
                        background: #ffffff;
                    }
                    :deep(.uni-numbox__value) {
                        flex: 1;
                        background: transparent !important;
                        font-weight: 400;
                        font-size: 30rpx;
                        color: #333333;
                        line-height: 42rpx;
                    }
                    :deep(.uni-numbox-btns) {
                        width: 48rpx;
                        height: 48rpx;
                        padding: 0;
                        .uni-numbox--text {
                            color: #fff !important;
                        }
                        background: var(--primary-color) !important;
                    }
                }
                .remarks {
                    :deep(is-focused) {
                        border-color: var(--primary-color) !important;
                    }
                    .input_count {
                        display: inline-block;
                        color: $uni-text-color-grey;
                        font-size: 28rpx;
                        right: 10rpx;
                        padding-right: 10rpx;
                        bottom: 16rpx;
                        position: absolute;
                        background: #fff;
                    }
                    :deep(.input-padding) {
                        padding-bottom: 40rpx;
                    }
                }

                .upload_tip {
                    font-weight: 400;
                    font-size: 24rpx;
                    color: rgba(0, 0, 0, 0.45);
                    line-height: 40rpx;
                }
            }
        }
    }
}
</style>
