<template>
    <view class='content'>
        <template v-if="state.previewUrls.length">
            <button type="primary" plain="true" size="mini" class="file-btns" @click="fileDelete">
                删除重新上传
            </button>
            <view class="qz-item" v-for="(item, idx) in state.previewUrls" :key="idx">
                <image :src="item" class="a4-page" mode="widthFix" />
            </view>
        </template>

        <template v-else>
            <template v-if="!state.previewUrl">
                <view class="tips">
                    持DOC、PDF、Excel、PNG、JPG格式文件，最多上传一个，最大为5M，再次上传会替代原文内容。需要套红的文档请上传docx后缀格式的文件
                </view>
                <view class="file-picker">
                    <!-- #ifndef MP-WEIXIN -->
                    <uni-file-picker ref="files" file-mediatype="all" :auto-upload="false" @select="fileSelect"
                        @delete="fileDelete">
                        <button type="primary" plain="true" size="mini" class="file-btn">
                            上传文件
                        </button>
                    </uni-file-picker>
                    <!-- #endif -->
                    <!-- #ifdef MP-WEIXIN -->
                    <view @click="chooseFileForMP">
                        <button type="primary" plain="true" size="mini" class="file-btn">上传文件</button>
                    </view>
                    <!-- #endif -->
                </view>
            </template>
            <template v-else>
                <view class="preview-item">
                    <!-- #ifndef MP-WEIXIN -->
                    <text>{{ state.attachments.name }}</text>
                    <!-- #endif -->
                    <!-- #ifdef MP-WEIXIN -->
                    <text @click="handleLook">正文预览</text>
                    <!-- #endif -->
                    <uni-icons type="closeempty" size="20" @click="fileDelete"></uni-icons>
                </view>
                <iframe :src="state.previewUrl" frameborder="0" class="a4-page"></iframe>
            </template>


        </template>
    </view>
</template>

<script setup>
import useStore from "@/store"
import { Base64 } from "js-base64"
import { previewFileByUrl } from "@/utils/index"
import { onMounted } from "vue"
const { officialDocs } = useStore()
const _formListData = computed(() => officialDocs.getDocsParams)
const state = reactive({
    id: '',
    previewUrl: '',
    previewUrls: [],
    loading: false,
    attachments: {}
})
// 查看附件
const handleLook = () => {
    previewFileByUrl(state.attachments.url)
}
// 获取正文
const initPage = async () => {
    uni.showToast({
        title: '加载中...',
        icon: "loading"
    })
    state.loading = true
    const documentId = _formListData.value.id
    http.post("/cloud/official-doc/document/preview", { documentId }).then(({ data }) => {
        state.previewUrls = data.previewUrls
        uni.hideToast()
        state.loading = false
    }).catch(() => {
        uni.hideToast()
        state.loading = false
    })
}
const fileSelect = ({ tempFiles }, item) => {
    try {
        tempFiles.forEach((v) => {
            const { path, name, uuid, size } = v
            uni.showToast({
                title: '加载中...',
                icon: "loading"
            })
            http._uploadFile("/cloud/official-doc/file/upload", path, { folderType: "app" }).then(({ data }) => {
                const codeUrL = encodeURIComponent(Base64.encode(data.url))
                state.previewUrl = `${import.meta.env.VITE_BASE_FILE_PREVIEW}/onlinePreview?url=${codeUrL}`
                state.attachments = data
                officialDocs.setSubmitFormListKey('fileUrl', data.url)
                officialDocs.setSubmitFormListKey('fileId', data.id)
                uni.hideToast()
                // initPage(data.id)
            }).catch(() => {
                uni.hideToast()
            })
        })
    } catch (err) {
        console.log(err)
    }
}
const fileDelete = () => {
    state.previewUrls = []
    state.previewUrl = ''
    officialDocs.setSubmitFormListKey('fileUrl', '')
    officialDocs.setSubmitFormListKey('fileId', '')
}

// 小程序文件选择方法
const chooseFileForMP = () => {
    // 使用uni.chooseMessageFile兼容小程序 从聊天记录中选择文件。
    uni.showActionSheet({
        itemList: ["从相册选择图片", "从聊天记录选择文件"],
        success: (res) => {
            if (res.tapIndex === 0) {
                // 选择图片
                uni.chooseImage({
                    count: 1,
                    success: (res) => {
                        fileSelect({ tempFiles: res.tempFiles })
                    }
                })
            } else if (res.tapIndex === 1) {
                // 从聊天记录选择
                uni.chooseMessageFile({
                    count: 1,
                    success: (res) => {
                        fileSelect({ tempFiles: res.tempFiles })
                    }
                })
            }
        }
    })
}
onMounted(() => {
    if (_formListData.value.status == "NI_GAO") {
        initPage()
    }
})
</script>

<style lang='scss' scoped>
.content {
    padding: 20rpx;
    overflow: hidden auto;

    .file-btns {
        position: fixed;
        bottom: 58rpx;
        z-index: 999999;
        right: 200rpx;
        margin: 0 10rpx;
        padding-top: 6rpx;
        padding-bottom: 6rpx;
        // border: 1px solid $uni-text-color-grey;
        // background-color: $uni-text-color-inverse;
        color: red;

        &[type="primary"] {
            // background-color: $uni-color-primary;
            border: 1px solid red
        }
    }

    .tips {
        text-align: center;
        font-size: 26rpx;
        color: #999;
    }

    .file-picker {
        text-align: center;
        margin: 20rpx 0;

        .file-btn {
            color: #00b781;
            border-color: #00b781;
        }

        :deep(.uni-file-picker__lists) {
            display: none;
        }
    }

    .a4-page {
        width: calc(100vw - 20rpx);
        height: calc(100vh - 550rpx);
    }

    .preview-item {
        padding-bottom: 20rpx;
        display: flex;
        justify-content: space-between;
        align-items: center;
        color: #999;

    }

    .empty {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
    }
}
</style>