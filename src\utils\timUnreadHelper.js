/**
 * 腾讯IM未读消息数量获取工具
 * 用于在外部页面获取IM未读消息总数
 */

import { TUILogin } from "@tencentcloud/tui-core"
import { TUIStore, StoreName } from "@tencentcloud/chat-uikit-engine"

/**
 * 获取腾讯IM未读消息总数
 * @returns {Promise<number>} 未读消息总数
 */
export function getTIMUnreadCount() {
    return new Promise((resolve, reject) => {
        try {
            // 通过TUILogin获取chat实例
            const { chat } = TUILogin.getContext()
            
            if (!chat) {
                console.warn('TIM chat实例未初始化')
                resolve(0)
                return
            }

            if (!chat.isReady()) {
                console.warn('TIM chat实例未就绪')
                resolve(0)
                return
            }

            // 获取会话列表来计算未读消息总数
            chat.getConversationList()
                .then((imResponse) => {
                    let totalUnread = 0
                    
                    if (imResponse.data && imResponse.data.conversationList) {
                        imResponse.data.conversationList.forEach((conversation) => {
                            totalUnread += conversation.unreadCount || 0
                        })
                    }
                    
                    console.log('TIM未读消息总数:', totalUnread)
                    resolve(totalUnread)
                })
                .catch((error) => {
                    console.error('获取IM会话列表失败:', error)
                    reject(error)
                })
        } catch (error) {
            console.error('获取IM未读消息数失败:', error)
            reject(error)
        }
    })
}

/**
 * 监听腾讯IM未读消息数变化
 * @param {Function} callback 回调函数，参数为未读消息数
 * @returns {Function} 取消监听的函数
 */
export function watchTIMUnreadCount(callback) {
    if (typeof callback !== 'function') {
        console.error('watchTIMUnreadCount: callback必须是函数')
        return () => {}
    }

    try {
        // 监听TUIStore中的未读消息数变化
        TUIStore.watch(StoreName.CONV, {
            totalUnreadCount: (count) => {
                const unreadCount = count || 0
                console.log('TIM未读消息数变化:', unreadCount)
                callback(unreadCount)
            }
        })

        // 返回取消监听的函数
        return () => {
            try {
                TUIStore.unwatch(StoreName.CONV, {
                    totalUnreadCount: callback
                })
            } catch (error) {
                console.error('取消监听TIM未读消息数失败:', error)
            }
        }
    } catch (error) {
        console.error('监听IM未读消息数失败:', error)
        return () => {}
    }
}

/**
 * 等待TIM初始化完成后获取未读消息数
 * @param {number} maxRetries 最大重试次数，默认5次
 * @param {number} retryInterval 重试间隔，默认1000ms
 * @returns {Promise<number>} 未读消息总数
 */
export function waitForTIMAndGetUnreadCount(maxRetries = 5, retryInterval = 1000) {
    return new Promise((resolve) => {
        let retries = 0
        
        const tryGetCount = () => {
            getTIMUnreadCount()
                .then((count) => {
                    resolve(count)
                })
                .catch(() => {
                    retries++
                    if (retries < maxRetries) {
                        console.log(`TIM未就绪，${retryInterval}ms后重试 (${retries}/${maxRetries})`)
                        setTimeout(tryGetCount, retryInterval)
                    } else {
                        console.warn('TIM初始化超时，返回默认值0')
                        resolve(0)
                    }
                })
        }
        
        tryGetCount()
    })
}

/**
 * 检查TIM是否已经初始化并就绪
 * @returns {boolean} 是否就绪
 */
export function isTIMReady() {
    try {
        const { chat } = TUILogin.getContext()
        return chat && chat.isReady()
    } catch (error) {
        console.error('检查TIM状态失败:', error)
        return false
    }
}
