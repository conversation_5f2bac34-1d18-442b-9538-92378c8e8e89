<template>
    <!-- 订单详情 amount_detail-->
    <view class="amount_detail" ref="paging">
        <NavBar title="订单详情" @click="clickLeft"> </NavBar>
        <view class="content">
            <List :title="state.form.recordTypeName" :time="state.form.createTime" :money="state.form.optAmount"
                :status="state.form?.orderDetail?.orderStatus" @click="amountDetailsItem(state.form.id)">
                <template #slot-chapter v-if="[2, 3, 5, 7].includes(state.form.orderStatus)">
                    <image class="chapter" :src="imgStatus" />
                </template>
            </List>
            <view class="flow_statement">
                <uni-list-item :border="false" v-for="item in recordTypeObj[_returnParams.recordType].children"
                    :key="item.key" :title="item.name" :rightText="rightTextComput(item)" />
            </view>
        </view>
    </view>
</template>

<script setup>
import NavBar from "../components/navBar.vue"
import List from "../components/list.vue"
import useStore from "@/store"
const { useSmartCard } = useStore()
const _returnParams = computed(() => useSmartCard.getSmart.returnParams)
const _returnRoute = computed(() => useSmartCard.getSmart.returnRoute)

// 已支付： orderStatus: 2,3,4,5 退款状态判断： 等于2时，判断是否有部分退款的 ， 
// 3,4,5 直接展示： 3.审核中 4.退款中 5.退款失败
// 已关闭：orderStatus  6 ,7  ,6不展示信息， 7展示退款成功 
// successStatus 成功   failStatus 失败  processStatus 审核中  3. partialRefund 部分退款 
const imgStatus = computed(() => {
    const imgStatusMap = {
        2: 'partialRefund',   // 部分退款
        3: 'processStatus',   // 审核中
        // 4: 'processStatus',   // 退款中
        5: 'failStatus',   // 退款失败
        7: 'successStatus'   // 成功
    }
    if (state.form.orderDetail.orderStatus == 2) {
        if (state.form.orderDetail.refundStatus == 3) {
            return `https://file.1d1j.cn/cloud-mobile/smartCard/partialRefund.png`
        }
        return ''
    }
    return `https://file.1d1j.cn/cloud-mobile/smartCard/${imgStatusMap[state.form.orderDetail.orderStatus]}.png`
})
// 业务类型 1.一卡通充值 2.一卡通消费 3.一卡通退款 4.一卡通退款失败 5.一卡通注销
const orderDetail = [
    { name: "订单编号", key: "orderNo" },
    { name: "交易流水号", key: "tradeNo" },
    { name: "创建时间", key: "createTime" },
    { name: "支付时间", key: "payTime" },
    { name: "支付渠道", key: "payMethodName" },
]
const orderRefundDetail = [
    { name: "退款原因", key: "auditRemarks" },
    { name: "本次申请退款金额", key: "refundAmount" },
    { name: "已退款总额", key: "totalRefundAmount" },
    { name: "退款编号", key: "tradeNo" },
    { name: "申请退款时间", key: "createTime" },
    { name: "退款到账时间", key: "refundTime" },
    { name: "退款状态", key: "refundStatus" }, // 退款状态 0.无/审核中 1.退款中 2.退款完成 3.退款失败
    { name: "退款去向", key: "refundDestination" },
]
const recordTypeObj = {
    1: { // 一卡通充值
        key: 'orderDetail',
        children: orderDetail
    },
    2: { // 一卡通消费
        key: 'orderDetail',
        children: orderDetail
    },
    3: { // 一卡通退款
        key: 'orderRefundDetail',
        children: orderRefundDetail
    },
    4: { // 一卡通退款失败
        key: 'orderRefundDetail',
        children: orderRefundDetail
    },
    5: { // 一卡通注销
        key: 'cardCancelDetail',
        children: [
            { name: "注销原因", key: "remark" },
            { name: "退款金额", key: "refundAmount" },
            { name: "余额退回方式", key: "payMethod" },
            { name: "操作人", key: "createBy" },
            { name: "操作时间", key: "createTime" },
        ]
    }
}

const state = reactive({
    form: {
        createTime: '',
        optAmount: '',
        id: '',
        orderStatus: '',
    },
    id: '',
    recordType: 0,
    personId: ''
})

const rightTextComput = computed(() => {
    return item => {
        if (Object.keys(state.form).length) {
            const indx = Number(_returnParams.value.recordType)
            const refundStatusObj = {
                0: '无/审核中',
                1: '退款中',
                2: '退款完成',
                3: '退款失败'
            }
            const _key = state.form[recordTypeObj[indx]?.key] ? state.form[recordTypeObj[indx]?.key][item.key] : '-'
            //refundStatus 退款状态 0.无/审核中 1.退款中 2.退款完成 3.退款失败
            if (item.key === 'refundStatus') {
                return refundStatusObj[_key] || '0'
            }
            if (typeof _key === 'number') {
                return _key || '0'
            }
            return _key
        }
        return '-'
    }
})
// 详情
const initPage = async () => {
    const { data } = await http.post('/unicard/app/person/record-type-detail', { id: _returnParams.value.id })
    state.form = data
}
onShow(async () => {
    await initPage()
})

onLoad(() => {
    state.personId = _returnParams.value.personId || ''
})

function clickLeft() {
    const _route = _returnRoute.value.pop()
    uni.navigateTo({ url: _route })
}
</script>

<style lang="scss" scoped>
.amount_detail {
    background: linear-gradient(180deg, $uni-bg-color 0%, #f5f7f9 19%, #f2f7f8 100%);

    .content {
        height: calc(100vh - 120rpx);
        background-color: $uni-bg-color;
        padding: 0 30rpx;

        :deep(.list) {
            position: relative;
            padding: 0;

            .handle {
                padding-top: 30rpx;
            }

            .chapter {
                width: 160rpx;
                height: 130rpx;
                position: absolute;
                right: 160rpx;
                top: 0;
            }


        }

        .flow_statement {
            // 虚线上边框
            border-top: 1rpx dashed $uni-border-color;
            padding: 20rpx 0;
            margin: 14rpx 0;

            :deep(.uni-list-item__container) {
                padding: 15rpx 0;

                .uni-list-item__extra-text {
                    font-size: 28rpx;
                }
            }
        }
    }
}
</style>
