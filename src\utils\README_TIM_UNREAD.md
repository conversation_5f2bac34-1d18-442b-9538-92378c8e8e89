# 腾讯IM未读消息数获取工具使用说明

## 功能说明

本工具提供了在外部页面获取腾讯IM未读消息总数的功能，支持：
- 获取当前用户的所有会话未读消息总数
- 实时监听未读消息数变化
- 错误处理和重试机制

## 文件说明

- `src/utils/timUnreadHelper.js` - 工具函数库
- `src/pages/chat/index.vue` - 使用示例（消息页面）

## 使用方法

### 1. 基本使用

```javascript
import { getTIMUnreadCount, watchTIMUnreadCount } from "@/utils/timUnreadHelper.js"

// 获取未读消息数
getTIMUnreadCount()
    .then((count) => {
        console.log('未读消息总数:', count)
        // 更新UI显示
    })
    .catch((error) => {
        console.error('获取失败:', error)
    })
```

### 2. 监听未读消息数变化

```javascript
// 开始监听
const unwatch = watchTIMUnreadCount((count) => {
    console.log('未读消息数变化:', count)
    // 更新UI显示
})

// 在组件销毁时取消监听
onUnmounted(() => {
    unwatch()
})
```

### 3. 在Vue组件中使用

```vue
<template>
    <div>
        <uni-badge :text="unreadCount > 0 ? unreadCount : null">
            <image src="@/static/message-icon.png" />
        </uni-badge>
    </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { getTIMUnreadCount, watchTIMUnreadCount } from "@/utils/timUnreadHelper.js"

const unreadCount = ref(0)
let unwatchFn = null

// 获取未读消息数
function refreshUnreadCount() {
    getTIMUnreadCount()
        .then((count) => {
            unreadCount.value = count
        })
        .catch((error) => {
            console.error('获取IM未读消息数失败:', error)
            unreadCount.value = 0
        })
}

onMounted(() => {
    // 开始监听未读消息数变化
    unwatchFn = watchTIMUnreadCount((count) => {
        unreadCount.value = count
    })
    
    // 初始获取一次
    setTimeout(() => {
        refreshUnreadCount()
    }, 1000)
})

onUnmounted(() => {
    // 取消监听
    if (unwatchFn) {
        unwatchFn()
    }
})
</script>
```

## API 说明

### getTIMUnreadCount()

获取腾讯IM未读消息总数

**返回值:** `Promise<number>` - 未读消息总数

**示例:**
```javascript
const count = await getTIMUnreadCount()
console.log('未读消息数:', count)
```

### watchTIMUnreadCount(callback)

监听腾讯IM未读消息数变化

**参数:**
- `callback: Function` - 回调函数，参数为未读消息数

**返回值:** `Function` - 取消监听的函数

**示例:**
```javascript
const unwatch = watchTIMUnreadCount((count) => {
    console.log('未读消息数:', count)
})

// 取消监听
unwatch()
```

### waitForTIMAndGetUnreadCount(maxRetries, retryInterval)

等待TIM初始化完成后获取未读消息数

**参数:**
- `maxRetries: number` - 最大重试次数，默认5次
- `retryInterval: number` - 重试间隔，默认1000ms

**返回值:** `Promise<number>` - 未读消息总数

### isTIMReady()

检查TIM是否已经初始化并就绪

**返回值:** `boolean` - 是否就绪

## 注意事项

1. 确保在调用前TUILogin已经成功登录
2. 建议在页面显示时刷新未读消息数
3. 在组件销毁时记得取消监听，避免内存泄漏
4. 工具函数包含错误处理，失败时会返回0

## 实现原理

1. 通过 `TUILogin.getContext()` 获取chat实例
2. 调用 `chat.getConversationList()` 获取会话列表
3. 遍历会话列表累加 `unreadCount` 字段
4. 通过 `TUIStore.watch` 监听未读消息数变化

## 错误处理

- TIM未初始化：返回0
- TIM未就绪：返回0
- 获取会话列表失败：抛出错误
- 监听失败：输出错误日志并返回空函数
