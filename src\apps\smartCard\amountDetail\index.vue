<template>
    <!-- 一卡通金额明细 amountDetail-->
    <z-paging class="amount_detail" ref="paging" v-model="state.dataList" @query="initPage" :auto="false">
        <template #top>
            <NavBar title="一卡通金额明细" :clickLeft="clickLeft" />
            <view class="card">
                <view>
                    <view class="tips">一卡通余额</view>
                    <view class="money">{{ state.balance }}</view>
                </view>
            </view>
        </template>
        <view class="content">
            <uni-section class="rese-section" size="normal" title="金额明细" type="line">
                <view class="list" v-for="item in state.dataList" :key="item.id">
                    <view class="handle">
                        <view class="title">{{ item.recordTypeName }}</view>
                        <view class="money" :class="{ active: item.optType == 1 }">
                            <text class="icon">{{ item.optType == 1 ? "+" : "-" }}</text>
                            {{ payAmount(item.optAmount) }}
                        </view>
                    </view>
                    <view class="main">
                        <view class="main-item time">时间：{{ item.createTime }}</view>
                    </view>
                    <view class="footer">
                        <button class="butn-default" type="primary" plain="true" size="mini"
                            @click="cardDetailsRefundItem(item)">查看详情</button>
                    </view>
                </view>
            </uni-section>
        </view>
        <template #empty>
            <yd-empty text="暂无数据" />
        </template>
    </z-paging>
</template>

<script setup>
import NavBar from "../components/navBar.vue"
import { payAmount } from "../components/index.js"
import useStore from "@/store"
const { useSmartCard } = useStore()
const _returnParams = computed(() => useSmartCard.getSmart.returnParams)
const _returnRoute = computed(() => useSmartCard.getSmart.returnRoute)
const paging = ref(null)
const state = reactive({
    dataList: [],
    personId: "",
    balance: 0
})

// 查看详情
const cardDetailsRefundItem = (item) => {
    const { id, recordType } = item
    useSmartCard.setReturnParams({
        ..._returnParams.value,
        id, recordType,
        personId: state.personId
    })
    useSmartCard.setReturnRoute([..._returnRoute.value, '/apps/smartCard/amountDetail/index'])
    navigateTo({
        url: `/apps/smartCard/amountDetail/detail`,
    })
}

const initPage = (pageNo, pageSize) => {
    const params = {
        pageNo,
        pageSize,
        personId: state.personId
    }
    http.post("/unicard/app/person/account-record-page", params)
        .then(({ data }) => {
            paging.value?.complete(data.list || [])
        })
        .catch(() => {
            paging.value?.complete([])
        })
}
// 余额
const balanceInfo = () => {
    http.post("/unicard/app/person/balance", { personId: state.personId }).then(({ data }) => {
        state.balance = data.balance || 0
    })
}
onMounted(async () => {
    balanceInfo()
    paging.value?.reload()
})
onLoad(() => {
    state.personId = _returnParams.value.personId || ""
})

function clickLeft() {
    const _route = _returnRoute.value.pop()
    uni.navigateTo({ url: _route })
}
</script>

<style lang="scss" scoped>
.amount_detail {
    .card {
        background: url("https://file.1d1j.cn/cloud-mobile/smartCard/detaileBanner.png") no-repeat center center;
        background-size: cover;
        margin: 20rpx;
        padding: 30rpx;
        max-height: 180rpx;
        font-weight: 500;
        display: flex;
        align-items: center;

        .tips {
            font-size: 24rpx;
            margin-bottom: 16rpx;
        }

        .money {
            font-size: 48rpx;
        }
    }

    .content {
        margin: 20rpx;

        :deep(.uni-section-content),
        :deep(.uni-section-header) {
            padding: 0 !important;
        }

        .list {
            background: #f7f7f7;
            border-radius: 16rpx;
            padding: 24rpx;
            margin: 20rpx 0;

            .handle {
                display: flex;
                justify-content: space-between;
                align-items: center;
                color: $uni-text-color;

                .title {
                    font-weight: 400;
                    font-size: 32rpx;
                }

                .money {
                    font-weight: 600;
                    font-size: 36rpx;

                    &.active {
                        color: var(--primary-color);
                    }
                }
            }

            .main {
                .main-item {
                    margin: 16rpx 0;
                    font-weight: 400;
                    font-size: 28rpx;
                    color: $uni-text-color-grey;
                }
            }

            .footer {
                text-align: right;

                .butn-default {
                    background: $uni-bg-color;
                    border-radius: 36rpx;
                    border: 2rpx solid #dfdfdf;
                    font-weight: 400;
                    font-size: 28rpx;
                    color: #666666;
                    padding: 0 22rpx;
                }
            }
        }

        .rese-section {
            :deep(.line) {
                background-color: var(--primary-color);
            }
        }
    }
}
</style>
