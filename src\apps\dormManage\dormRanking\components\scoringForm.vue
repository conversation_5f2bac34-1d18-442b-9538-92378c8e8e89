<template>
    <view>
        <view class="list_class">
            <view class="item" v-for="(item, index) in scoreList" :key="index">
                <view class="title">{{ item.firstIndicatorName }}</view>
                <view class="norm_list" v-for="(norm, normIndex) in item.indicatorDetailList" :key="normIndex + 'norm'">
                    <view class="norm_title">
                        {{ norm.scoreStandard }}
                    </view>
                    <view class="norm_score">
                        <text class="text">得分</text>
                        <view>
                            <uni-number-box :min="norm.scoreRangeMin || 0" :max="norm.scoreRangeMax || 10" v-model="norm.initializeScore" :step="norm.minScoreUnit || 0.5" />
                        </view>
                    </view>
                    <view class="divider"></view>
                    <view class="norm_score">
                        <text class="text">备注</text>
                        <textarea class="textarea_class" :adjust-position="false" placeholder-class="placeholder_class" autoHeight v-model="norm.remarked" placeholder="请输入"></textarea>
                    </view>
                    <div class="divider"></div>
                </view>
            </view>
        </view>
        <!-- 底部 -->
        <div class="footer_box">
            <button class="footer_btn" @click="submitForm" :loading="loading">提交</button>
        </div>
    </view>
</template>

<script setup>
const emit = defineEmits(["submitForm"])
const props = defineProps({
    loading: {
        type: Boolean,
        default: false
    },
    // 打分指标数组
    scoreList: {
        type: Array,
        default: () => []
    }
})

const loading = computed(() => {
    return props.loading
})

const scoreList = computed(() => {
    return props.scoreList
})

function submitForm() {
    emit("submitForm", scoreList.value)
}
</script>

<style lang="scss" scoped>
.list_class {
    .item {
        display: flex;
        flex-direction: column;
        margin-bottom: 20rpx;
        min-height: 400rpx;
        background: #fff;
        padding: 30rpx;
        .title {
            font-size: 28rpx;
            font-weight: 600;
            color: #333333;
            line-height: 40rpx;
        }
        .norm_list {
            display: flex;
            margin-top: 30rpx;
            flex-direction: column;
            .norm_title {
                font-size: 28rpx;
                font-weight: 400;
                color: #4566d5;
                line-height: 40rpx;
            }
            .norm_score {
                padding: 34rpx 0rpx;
                display: flex;
                align-items: center;
                justify-content: space-between;
                .text {
                    font-size: 28rpx;
                    font-weight: 400;
                    color: #333333;
                    line-height: 40rpx;
                }
                .textarea_class {
                    width: 72%;
                    font-size: 28rpx;
                    font-weight: 400;
                    color: #999999;
                    line-height: 40rpx;
                    text-align: right;
                }
                .placeholder_class {
                    font-size: 28rpx;
                    font-weight: 400;
                    color: #999999;
                    line-height: 40rpx;
                }
            }
            .divider {
                height: 2rpx;
                background: #d8d8d8;
            }
        }
    }
}
.footer_box {
    width: calc(100vw - 60rpx);
    height: 106rpx;
    background: #ffffff;
    position: fixed;
    bottom: 0rpx;
    left: 0rpx;
    padding: 30rpx;
    z-index: 99;

    .footer_btn {
        background: #4566d5;
        border-radius: 10rpx;
        font-size: 32rpx;
        height: 92rpx;
        font-weight: 400;
        color: #ffffff;
        line-height: 92rpx;
    }
}
</style>
