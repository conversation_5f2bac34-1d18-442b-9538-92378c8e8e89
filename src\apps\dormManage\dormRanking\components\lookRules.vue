<template>
    <div>
        <uni-popup ref="rulesPopup" type="bottom" :safe-area="false">
            <div class="look_rules_popup">
                <div class="title">
                    <span class="text">评分规则</span>
					 <uni-icons class="closeempty_icons" type="closeempty" size="22" color="#333" @click="closeFn"></uni-icons>
                </div>
                <div class="rulse">
                    <div class="rulse_list" v-for="(item, index) in scoreList" :key="index">
                        <span class="title">{{ "一级指标：" + item.firstIndicatorName }}</span>
                        <div class="norm_list" v-for="(norm, normIndex) in item.indicatorDetailList" :key="normIndex">
                            <div class="norm_item">
                                <div>二级指标：</div>
                                <div>{{ norm.secondIndicatorName || "-" }}</div>
                            </div>
                            <div class="norm_item">
                                <div>评分范围：</div>
                                <div>{{ norm.scoreRange || "-" }}</div>
                            </div>
                            <div class="norm_item">
                                <div>评分标准：</div>
                                <div>
                                    {{ norm.scoreStandard || "-" }}
                                </div>
                            </div>
                            <div class="norm_item">
                                <div>项目满分值：</div>
                                <div>{{ norm.basicScore || "-" }}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </uni-popup>
    </div>
</template>

<script setup>
import { ref, reactive, watch, computed } from "vue"
const state = reactive({})
const rulesPopup = ref()
const $emit = defineEmits(["closePopup"])
const $props = defineProps({
    isShow: {
        type: Boolean,
        default: false
    },
    scoreList: {
        type: Array,
        default: () => []
    }
})

const scoreList = computed(() => {
    return $props.scoreList
})

watch(
    () => $props.isShow,
    (value) => {
        value ? rulesPopup.value.open() : rulesPopup.value.close()
    }
)

function closeFn() {
    $emit("closePopup", false)
}
</script>

<style lang="scss" scoped>
// 选择周
.look_rules_popup {
    height: 60vh;
    background: #fff;
    border-radius: 20rpx 20rpx 0rpx 0rpx;
    padding-bottom: 90rpx;

    .title {
        height: 100rpx;
        background: #fff;
        display: flex;
        align-items: center;

        .text {
            font-size: 34rpx;
            font-family:
                PingFangSC-Medium,
                PingFang SC;
            font-weight: 500;
            color: #333333;
            line-height: 48rpx;
            width: 100vh;
            text-align: center;
        }

        .closeempty_icons {
            margin-right: 18rpx;
        }
    }
    .rulse {
        max-height: 90%;
        overflow-y: auto;
    }
    .rulse_list {
        display: flex;
        flex-direction: column;
        min-height: 300rpx;
        padding: 0rpx 30rpx;
        .title {
            font-size: 28rpx;
            font-family:
                PingFangSC-Semibold,
                PingFang SC;
            font-weight: 600;
            color: #333333;
            line-height: 40rpx;
        }
        .norm_list {
            min-height: 220rpx;
            background: $uni-bg-color-grey;
            border-radius: 10rpx;
            margin-bottom: 20rpx;
            display: flex;
            flex-direction: column;
            padding: 30rpx;
            justify-content: space-between;
            .norm_item {
                display: flex;
                padding: 16rpx 0rpx;
                :nth-child(1) {
                    font-size: 28rpx;
                    font-weight: 400;
                    color: #999999;
                    line-height: 40rpx;
                    min-width: 150rpx;
                }
                :nth-child(2) {
                    font-size: 28rpx;
                    font-weight: 400;
                    color: #333333;
                    line-height: 40rpx;
                }
            }
        }
    }
}
</style>
