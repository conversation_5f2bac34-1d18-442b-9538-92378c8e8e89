<template>
  <view class="pay-password-setting">
    <!-- 头部区域 -->
    <NavBar title="新支付密码" :clickLeft="handleBack" />

    <!-- 内容区域 -->
    <view class="content">
      <!-- 步骤1：设置密码 -->
      <view v-if="currentStep === 1" class="step-content">
        <view class="step-title">设置密码，用于支付验证</view>
        <view class="step-subtitle">不能是连续或密码或连续数字</view>

        <!-- 密码输入框 -->
        <view class="password-input" @click="openKeyBoard">
          <view class="input-box" v-for="(item, index) in 6" :key="index"
            :class="{ 'filled': index < state.newPwd.length, 'error': hasError }">
            <text v-if="index < state.newPwd.length" class="password-dot">•</text>
          </view>
        </view>

        <!-- 错误提示 -->
        <view v-if="errorMessage" class="error-message">
          <text class="error-text">{{ errorMessage }}</text>
        </view>
      </view>

      <!-- 步骤2：确认密码 -->
      <view v-if="currentStep === 2" class="step-content">
        <view class="step-title">请再次输入，以确认密码</view>
        <view class="step-subtitle">不能是连续或密码或连续数字</view>

        <!-- 密码输入框 -->
        <view class="password-input" @click="openKeyBoard">
          <view class="input-box" v-for="(item, index) in 6" :key="index"
            :class="{ 'filled': index < state.confirmPwd.length, 'error': hasError }">
            <text v-if="index < state.confirmPwd.length" class="password-dot">•</text>
          </view>
        </view>

        <!-- 错误提示 -->
        <view v-if="errorMessage" class="error-message">
          <text class="error-text">{{ errorMessage }}</text>
        </view>
      </view>
    </view>

    <!-- 数字键盘 -->
    <uv-keyboard ref="keyboard" mode="number" :tooltip="false" :dotDisabled="true" :safeAreaInsetBottom="true"
      @backspace="keyboardBackspace" @change="keyboardChange"></uv-keyboard>
  </view>
</template>

<script setup>
import NavBar from "../components/navBar.vue"
import { encrypt } from "@/utils/rsa";
import useStore from "@/store"
const { useSmartCard } = useStore()
const _returnParams = computed(() => useSmartCard.getSmart.returnParams)
const _returnRoute = computed(() => useSmartCard.getSmart.returnRoute)
const keyboard = ref(null)
// 组件属性定义
const props = defineProps({
  // 设置密码的API函数
  setPasswordFunction: {
    type: Function,
    default: null
  },
  // 是否是修改密码模式（需要先验证旧密码）
  isModifyMode: {
    type: Boolean,
    default: false
  }
})

// 组件事件定义
const emit = defineEmits([
  'success',    // 设置成功
  'failed',     // 设置失败
  'cancel',     // 取消操作
  'back'        // 返回上一步
])
const state = reactive({
  id: '',
  newPwd: '',
  confirmPwd: ''
})
// 响应式数据
const currentStep = ref(1)  // 当前步骤：1-设置密码，2-确认密码
const errorMessage = ref('')    // 错误信息
const hasError = ref(false)     // 是否有错误
const isSubmitting = ref(false) // 是否正在提交

/**
 * 验证密码安全性
 * @param {string} password - 要验证的密码
 * @returns {object} 验证结果
 */
const validatePassword = (password) => {
  if (password.length !== 6) {
    return { valid: false, message: '密码必须为6位数字' }
  }

  // 检查是否为纯数字
  if (!/^\d{6}$/.test(password)) {
    return { valid: false, message: '密码只能包含数字' }
  }

  // 检查连续数字（如123456、654321）
  let hasConsecutive = false
  for (let i = 0; i < password.length - 2; i++) {
    const num1 = parseInt(password[i])
    const num2 = parseInt(password[i + 1])
    const num3 = parseInt(password[i + 2])

    // 检查递增连续
    if (num2 === num1 + 1 && num3 === num2 + 1) {
      hasConsecutive = true
      break
    }

    // 检查递减连续
    if (num2 === num1 - 1 && num3 === num2 - 1) {
      hasConsecutive = true
      break
    }
  }

  if (hasConsecutive) {
    return { valid: false, message: '不能包含连续的三个数字' }
  }

  // 检查重复数字（如111、222、333等）
  let hasRepeated = false
  for (let i = 0; i < password.length - 2; i++) {
    if (password[i] === password[i + 1] && password[i + 1] === password[i + 2]) {
      hasRepeated = true
      break
    }
  }

  if (hasRepeated) {
    return { valid: false, message: '不能包含重复的三个数字' }
  }

  return { valid: true, message: '' }
}
// 打开键盘
const openKeyBoard = () => {
  keyboard.value.open()
}
/**
 * 处理键盘按键点击
 * @param {string} key - 按键值
 */
const keyboardChange = (e) => {
  if (currentStep.value == 1) {
    hasError.value = false
    errorMessage.value = ''
    state.newPwd += e
    // if (state.newPwd.length == 6) {
    //   keyboard.value.close()
    // }
  }
  if (currentStep.value == 2) {
    hasError.value = false
    errorMessage.value = ''
    state.confirmPwd += e
    if (state.confirmPwd.length == 6) {
      keyboard.value.close()
    }
  }
}
//键盘中的 删除
const keyboardBackspace = () => {
  if (currentStep.value == 1 && state.newPwd) {
    state.newPwd = state.newPwd.slice(0, -1)
  }
  if (currentStep.value == 2 && state.confirmPwd) {
    state.confirmPwd = state.confirmPwd.slice(0, -1)
  }
}

/**
 * 监听第一次密码输入完成
 */
watch(() => state.newPwd, (newVal) => {
  if (newVal.length === 6) {
    // 验证密码安全性
    const validation = validatePassword(newVal)
    if (!validation.valid) {
      showError(validation.message)
      // 延迟清空密码
      setTimeout(() => {
        state.newPwd = ''
      }, 1000)
    } else {
      // 密码有效，进入下一步
      setTimeout(() => {
        currentStep.value = 2
      }, 500)
    }
  }
})

/**
 * 监听第二次密码输入完成
 */
watch(() => state.confirmPwd, (newVal) => {
  if (newVal.length === 6) {
    // 检查两次密码是否一致
    if (newVal !== state.newPwd) {
      showError('两次输入的密码不一致')
      setTimeout(() => {
        state.confirmPwd = ''
      }, 1000)
    } else {

      state.id = _returnParams.value.personId
      const parasm = {
        paramEncipher: encrypt(JSON.stringify(state))
      }

      http.post('/unicard/app/person/updatePassword', parasm).then(({ data }) => {
        if (data) {
          uni.showToast({
            icon: "none",
            title: "密码设置成功！",
          })
          useSmartCard.setPassword(data)
          // 越级跳转返回
          if (_returnParams.value.crossRoute) {
            _returnRoute.value.pop()
            _returnRoute.value.pop()
            navigateTo({ url: _returnParams.value.crossRoute })
          } else {
            handleBack()
          }
        }
      }).catch((error) => {
        state.confirmPwd = ''
        if (error.code = 1030006017) {
          currentStep.value = 1
           state.newPwd = ''
        }
      })
    }
  }
})
/**
 * 显示错误信息
 * @param {string} message - 错误信息
 */
const showError = (message) => {
  errorMessage.value = message
  hasError.value = true

  // 震动反馈
  uni.vibrateShort()
}

/**
 * 设置成功处理
 */
const handleSuccess = () => {
  emit('success', state.newPwd)
}

/**
 * 设置失败处理
 * @param {string} message - 错误信息
 */
const handleFailed = (message) => {
  showError(message)
  emit('failed', message)
}

/**
 * 返回处理
 */
const handleBack = () => {
  uni.navigateBack()
}

/**
 * 重置组件状态
 */
const reset = () => {
  currentStep.value = 1
  state.newPwd = ''
  state.confirmPwd = ''
  errorMessage.value = ''
  hasError.value = false
  isSubmitting.value = false
}
// 暴露方法给父组件
defineExpose({
  reset
})
</script>

<style lang="scss" scoped>
.pay-password-setting {
  height: 100vh;
  background-color: #fff;
  display: flex;
  flex-direction: column;
}

/* 内容区域 */
.content {
  flex: 1;
  padding: 36rpx 32rpx 40rpx;

  .step-content {

    .step-title {
      margin-bottom: 8rpx;
      font-weight: 600;
      font-size: 40rpx;
      color: #181818;
    }

    .step-subtitle {
      margin-bottom: 40rpx;
      font-weight: 400;
      font-size: 28rpx;
      color: #666666;
    }
  }
}

/* 密码输入框 */
.password-input {
  display: flex;
  gap: 30rpx;
  margin-bottom: 40rpx;

  .input-box {
    width: 84rpx;
    height: 84rpx;
    border: 2rpx solid #e0e0e0;
    border-radius: 16rpx;
    display: flex;
    justify-content: center;
    background-color: #ffffff;
    transition: all 0.3s ease;

    &.filled {
      border-color: #4CAF50;
      background-color: #f0f8ff;
    }

    &.error {
      border-color: #ff4757;
      background-color: #fff5f5;
      animation: shake 0.5s ease-in-out;
    }

    .password-dot {
      font-size: 48rpx;
      color: #333333;
      font-weight: bold;
    }
  }
}

/* 错误提示 */
.error-message {
  text-align: center;
  margin-bottom: 32rpx;

  .error-text {
    font-size: 28rpx;
    color: #ff4757;
  }
}



/* 震动动画 */
@keyframes shake {

  0%,
  100% {
    transform: translateX(0);
  }

  10%,
  30%,
  50%,
  70%,
  90% {
    transform: translateX(-10rpx);
  }

  20%,
  40%,
  60%,
  80% {
    transform: translateX(10rpx);
  }
}

/* 提交中状态 */
.submitting {
  .keyboard-key {
    opacity: 0.6;
    pointer-events: none;
  }
}
</style>
