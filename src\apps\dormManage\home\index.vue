<template>
    <yd-page-view ref="page" class="home_page" title="首页" :hideLeft="true" :hideBottom="hideBottom" :tabBarCurrent="0">
        <div class="home_page">
            <Notice :data="state.notice" />
            <div class="page_class">
                <!-- 楼栋卡片 -->
                <div class="building_box">
                    <div class="building">
                        <div class="select_building" @click="selectBuilding">
                            <img class="image" src="https://alicdn.1d1j.cn/announcement/20230724/4dda454f82ea4117b695ca71e962dcc3.png" alt="" />
                            <span class="title">{{ state.buildName }}</span>
                            <img class="icon" src="https://alicdn.1d1j.cn/announcement/20230724/ae1aa1e2820447ef8626a48ff74ed0ed.png" alt="" />
                        </div>
                        <div class="gradient"></div>
                        <div class="statistics">
                            <div class="item_statistics">
                                <div class="text">
                                    <span class="num">{{ state.info.totalCheckInPeopleCount }}</span
                                    >人
                                </div>
                                <div class="title">总入住人数</div>
                            </div>
                            <div class="divider"></div>
                            <div class="item_statistics">
                                <div class="text">
                                    <span class="num">{{ state.info.manageRoomCount }}</span
                                    >间
                                </div>
                                <div class="title">管理寝室数</div>
                            </div>
                            <div class="divider"></div>
                            <div class="item_statistics">
                                <div class="text">
                                    <span class="num">{{ state.info.manageBedCount }}</span
                                    >张
                                </div>
                                <div class="title">管理床位数</div>
                            </div>
                        </div>
                    </div>
                    <div class="attendance">
                        <div class="time" @click="selectAtten">
                            <view>
                                <text v-if="state.attendanceInfo.sequence">{{ state.attendanceInfo.sequence }}：{{ state.attendanceInfo.value }}</text>
								<text v-else>暂无考勤</text>
                            </view>
                            <uni-icons type="right" size="18" color="#979797"></uni-icons>
                        </div>
                        <div class="data">
                            <div class="left">
                                正常归寝<span class="num">{{ state.attendanceInfo.normalNum || 0 }}</span
                                >人
                            </div>
                            <div class="right">
                                应归<span class="num">{{ state.attendanceInfo.totalNum || 0 }}</span
                                >人
                            </div>
                        </div>
                        <progress class="progress" :percent="percent" stroke-width="12" activeColor="#4566D5" backgroundColor="#f6faff" />
                    </div>
                </div>
                <!-- 状态统计 -->
                <CountCom :data="state.attendanceInfo" />
                <!-- 排名 -->
                <DormitoryRanking @lookInfo="lookInfo" :data="state.overview" />
                <!-- 宿舍通行 -->
                <dormitoryAccess :student="state.student" :teacher="state.teacher"></dormitoryAccess>
            </div>
        </div>
    </yd-page-view>
    <yd-select-popup ref="selectBuildingRef" :list="state.buildingList" :fieldNames="{ value: 'buildingId', label: 'name' }" title="选择楼栋" @closePopup="closeBuild" :selectId="[state.buildingId]" />
    <yd-select-popup ref="attendanceRef" :list="state.attendanceList" title="选择考勤类型" @closePopup="closeAtten" :fieldNames="{ value: 'attendanceTimeId', label: 'attendanceName' }" :selectId="[state.attendanceInfo?.attendanceTimeId]" />
</template>

<script setup>
import selectPopup from "../components/selectPopup.vue"
import CountCom from "./components/countCom.vue"
import DormitoryRanking from "./components/dormitoryRanking.vue"
import Notice from "./components/noticeCom.vue"
import dormitoryAccess from "./components/dormitoryAccess.vue"
const hideBottom = ref(false)

const { system } = store()

const state = reactive({
    buildingId: "",
    buildName: "",
    buildingList: [],
    attendanceList: [],
    info: {},
    student: {},
    teacher: {},
    notice: {},
    attendanceInfo: {},
    overview: {
        rankList: [],
        lastList: []
    }
})

const percent = computed(() => {
    if (!state.attendanceInfo.normalNum && !state.attendanceInfo.totalNum) return 0
    return Math.ceil((state.attendanceInfo.normalNum / state.attendanceInfo.totalNum) * 100)
})

const selectBuildingRef = ref(null)

onLoad((options) => {
    Object.keys(options).forEach((key) => {
        options[key] = decodeURIComponent(options[key])
    })
    hideBottom.value = Boolean(options.hideBottom) || false
})

function selectBuilding() {
    selectBuildingRef.value.open()
}

function lookInfo() {
    navigateTo({
        url: "/apps/dormManage/dormRanking/ranking"
    })
}

const attendanceRef = ref(null)
const selectAtten = () => {
    attendanceRef.value.open()
}

// 选择考勤
const closeAtten = (val) => {
    if (!val) return
    state.attendanceInfo = val
}

// 获取楼栋
const getBuildList = async () => {
    const { data } = await http.post("/app/dormitory/managerHomepage/buildingOverview", { buildingId: state.buildingId })
    state.buildingList = data.constructList
    const item = data.constructList.find((i) => i.checked == true)
    state.buildName = item.name
    state.buildingId = item.buildingId
    // 设置子应用数据
    system.setAppData({ sys: "dormManage", data: { buildingId: item.buildingId } })
    Object.assign(state.info, data)
}

// 宿舍通行
const countDormPass = async () => {
    const { data } = await http.post("/app/dorm/pass/countDormPass", { buildingId: state.buildingId })
    Object.keys(data).forEach((key) => (state[key] = data[key]))
}
// 信息列表
const pageIndex = async () => {
    const {
        data: { list }
    } = await http.post("/app/mobile/mess/receive/pageIndex", { pageNo: 1, pageSize: 1, identifier: "announcement" })
    if (!list.length) return
    state.notice = list[0]
}

// 获取考勤数据
const getAttendance = async () => {
    const { data } = await http.get("/app/dormitory/attendance/manage/home", { buildingId: state.buildingId })
    if (!data.length) {
		state.attendanceList = []
		state.attendanceInfo = {}
		return
	}
    state.attendanceList = data.map((i) => ({ ...i, label: i.sequence + " " + i.startTime + "-" + i.endTime, value: i.startTime + "-" + i.endTime }))
    state.attendanceInfo = state.attendanceList[0]
}

// 获取德育总览信息
const getOverview = async () => {
    const { data } = await http.post("/app/dormitory/managerHomepage/moralEducationOverview", { buildingId: state.buildingId })
    if (!data) {
    	state.overview = {}
		return
	}
    state.overview = data
}

// 选择楼栋关闭
const closeBuild = async (val) => {
    if (val.buildingId == state.buildingId) return
    state.buildName = val.name
    state.buildingId = val.buildingId
    // 设置子应用数据
    system.setAppData({ sys: "dormManage", data: { buildingId: val.buildingId } })
    await getBuildList()
    countDormPass()
    getAttendance()
    getOverview()
}

onMounted(async () => {
    pageIndex()
    await getBuildList()
    countDormPass()
    getAttendance()
    getOverview()
})
</script>

<style lang="scss" scoped>
.home_page {
    .page_class {
        padding: 28rpx;
        min-height: calc(100vh - 260rpx);
        background: #f6faff;
    }
    .building_box {
        min-height: 300rpx;
        width: 100%;
        .building {
            position: relative;
            width: 92%;
            height: 220rpx;
            padding: 28rpx;
            background: #4566d5;
            border-radius: 20px 20px 0px 0px;
            .select_building {
                width: 100%;
                display: flex;
                align-items: center;
                .image {
                    height: 40rpx;
                    width: 40rpx;
                    border-radius: 50%;
                }
                .triangle_class {
                    display: flex;
                    align-items: center;
                }
                .title {
                    font-size: 28rpx;
                    font-weight: 400;
                    color: #ffffff;
                    line-height: 40rpx;
                    margin: 0rpx 12rpx;
                }
                .icon {
                    height: 28rpx;
                    width: 28rpx;
                }
            }
            .gradient {
                position: absolute;
                bottom: 0rpx;
                left: 0;
                width: 100%;
                height: 180rpx;
                background: linear-gradient(180deg, #4566d5 0%, #ffffff 100%);
            }
            .statistics {
                position: absolute;
                bottom: 28rpx;
                width: 638rpx;
                height: 152rpx;
                background: #ffffff;
                border-radius: 20rpx;
                display: flex;
                justify-content: space-evenly;
                align-items: center;

                .item_statistics {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    .text {
                        font-size: 28rpx;
                        font-weight: 400;
                        color: #333333;
                        line-height: 40rpx;
                        .num {
                            font-size: 40rpx;
                            line-height: 58rpx;
                            padding-right: 8rpx;
                        }
                    }
                    .title {
                        font-size: 26rpx;
                        font-weight: 400;
                        color: #999999;
                        line-height: 36rpx;
                    }
                }
                .divider {
                    width: 1rpx;
                    height: 80rpx;
                    background: #d9d9d9;
                }
            }
        }
        .attendance {
            min-height: 120rpx;
            padding: 28rpx;
            background: #ffffff;
            border-radius: 0rpx 0rpx 20rpx 20rpx;

            .time {
                font-size: 26rpx;
                font-weight: 400;
                color: #999999;
                line-height: 36rpx;
                display: flex;
                align-items: center;
                justify-content: space-between;
            }
            .data {
                display: flex;
                justify-content: space-between;
                align-items: center;
                .left,
                .right {
                    font-size: 28rpx;
                    font-weight: 400;
                    color: #333333;
                    line-height: 40rpx;
                }
                .left .num {
                    font-size: 40rpx;
                    color: #4566d5;
                    margin: 0rpx 8rpx;
                    line-height: 58rpx;
                }

                .right .num {
                    font-size: 40rpx;
                    color: #333333;
                    line-height: 58rpx;
                    margin: 0rpx 8rpx;
                }
            }
            .progress {
                margin-top: 20rpx;
                :deep(.uni-progress-bar) {
                    border-radius: 20px;
                }
                :deep(.uni-progress-inner-bar) {
                    border-radius: 20px;
                }
            }
        }
    }
}
</style>
