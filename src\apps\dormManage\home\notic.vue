<template>
    <view class="notice_container">
        <z-paging>
            <template #top>
                <uni-nav-bar left-icon="left" fixed statusBar :border="false" :title="state.title" @clickLeft="back" :leftWidth="navBarLeftWidth()" :rightWidth="navBarRightWidth()"></uni-nav-bar>
            </template>
            <view class="main">
                <view class="top">{{ state.identityUserName }} 发布于 {{ state.timerDate }}</view>
                <view class="msg" v-if="state.content">
                    <rich-text :nodes="state.content"></rich-text>
                </view>
                <yd-empty v-else text="暂无内容" :isMargin="true" />
            </view>
        </z-paging>
    </view>
</template>

<script setup>
const state = ref({})

const back = () => {
    uni.navigateBack()
}

onMounted(async () => {
    const {
        data: { list }
    } = await http.post("/app/mobile/mess/receive/pageIndex", { pageNo: 1, pageSize: 1, identifier: "announcement" })
    if (!list.length) return
    state.value = list[0]
})
</script>

<style lang="scss" scoped>
.notice_container {
    background: #fafafafa;
    .main {
        padding: 30rpx;

        .top {
            font-size: 24rpx;
            color: #b5b5b5;
        }
        .msg {
            background: var(--primary-bg-color);
            padding: 20rpx;
            border-radius: 8rpx;
            margin-top: 30rpx;
            font-size: 28rpx;
            max-width: 100vw;
            // #ifdef H5 || H5-WEIXIN
            :deep(img) {
                max-width: 100%;
                height: auto;
            }
            // #endif
            // #ifdef MP-WEIXIN
            :deep(image) {
                max-width: 100%;
                height: auto;
            }
            // #endif
        }
    }
}
</style>
