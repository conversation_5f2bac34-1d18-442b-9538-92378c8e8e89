<template>
  <view class="payment-agreement-page">
    <NavBar :title="agreementData.title" :clickLeft="clickLeft" />
    <!-- 协议标题 -->
    <!-- <view class="agreement-header">
      <text class="agreement-title">{{ agreementData.title }}</text>
    </view> -->

    <!-- 协议内容 -->
    <view class="agreement-content">
      <view v-for="(section, index) in agreementData.sections" :key="index" class="agreement-section"
        :class="section.type">
        <!-- 警告类型 -->
        <view v-if="section.type === 'warning'" class="warning-section">
          <text class="section-title warning-title">{{ section.title }}</text>
          <text class="section-content">{{ section.content }}</text>
          <view v-if="section.items" class="section-items">
            <text v-for="(item, itemIndex) in section.items" :key="itemIndex" class="section-item">
              {{ item }}
            </text>
          </view>
        </view>

        <!-- 提示类型 -->
        <view v-else-if="section.type === 'notice'" class="notice-section">
          <text class="section-title notice-title">{{ section.title }}</text>
          <text class="section-content notice-content">{{
            section.content
          }}</text>
        </view>

        <!-- 普通章节 -->
        <view v-else class="normal-section">
          <text class="section-title">{{ section.title }}</text>
          <text v-if="section.content" class="section-content">{{
            section.content
          }}</text>
          <view v-if="section.items" class="section-items">
            <text v-for="(item, itemIndex) in section.items" :key="itemIndex" class="section-item">
              {{ item }}
            </text>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部按钮 -->
    <view class="agreement-footer">
      <button class="confirm-btn" @click="clickLeft">我已阅读</button>
    </view>
  </view>
</template>

<script setup>
import agreementJson from "@/static/agreement/payment-service-agreement.json";
import NavBar from "../components/navBar.vue"
// 协议数据
const agreementData = ref([]);
/**
 * 加载协议数据
 */
const loadAgreementData = () => {
  // 直接在代码中定义协议数据，确保稳定性
  agreementData.value = agreementJson;
};

/**
 * 确认按钮处理
 */

/**
 * 返回处理
 */
function clickLeft() {
  // 返回上一页
  uni.navigateBack();
}

// 页面挂载时加载协议数据
onMounted(() => {
  loadAgreementData();
});
</script>

<style lang="scss" scoped>
.payment-agreement-page {
  min-height: 100vh;
  background-color: #f7f7f7;
  display: flex;
  flex-direction: column;
}

/* 协议头部 */
// .agreement-header {
//   background-color: #ffffff;
//   padding: 40rpx 30rpx 30rpx;
//   border-bottom: 1rpx solid #f0f0f0;

//   .agreement-title {
//     font-size: 36rpx;
//     font-weight: 600;
//     color: #333333;
//     text-align: center;
//     display: block;
//   }
// }

/* 协议内容 */
.agreement-content {
  flex: 1;
  background-color: #ffffff;
  padding: 0 30rpx 200rpx;

  .agreement-section {
    margin-bottom: 40rpx;

    .section-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #333333;
      line-height: 1.6;
      display: block;
      margin-bottom: 20rpx;
    }

    .section-content {
      font-size: 28rpx;
      color: #666666;
      line-height: 1.8;
      display: block;
      margin-bottom: 16rpx;
      white-space: pre-line;
    }

    .section-items {
      .section-item {
        font-size: 28rpx;
        color: #666666;
        line-height: 1.8;
        display: block;
        margin-bottom: 12rpx;
        white-space: pre-line;
      }
    }

    /* 警告类型样式 */
    &.warning {
      .warning-title {
        color: #ff4757;
        font-weight: 700;
      }
    }

    /* 提示类型样式 */
    &.notice {
      .notice-title {
        color: #00b781;
        font-weight: 700;
      }

      .notice-content {
        background-color: #f8f9fa;
        padding: 20rpx;
        border-radius: 8rpx;
        border-left: 6rpx solid #00b781;
      }
    }
  }
}

/* 底部按钮 */
.agreement-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 32rpx;
  background-color: #ffffff;
  border-top: 1rpx solid #f0f0f0;
  z-index: 100;

  .confirm-btn {
    width: 100%;
    height: 92rpx;
    background: #11c685;
    border-radius: 10rpx;
    color: #ffffff;
    font-size: 32rpx;
    font-weight: 600;
    border: none;

    &:active {
      background-color: #00b781;
    }
  }
}
</style>
