<template>
  <view class="unbind-container">
    <NavBar title="银行卡绑定/解绑" :clickLeft="clickLeft" />
    <!-- 主要内容区域 -->
    <view class="content">
      <uni-list-item v-for="item in unbindList" :key="item.value" extra-icon="loop">
        <template v-slot:header>
          <view class="slot-box">
            <text class="slot-icon">*</text>
            <text class="slot-text">{{ item.label }}</text>
          </view>
        </template>
        <template v-slot:footer>

          <text class="slot-footer-text" v-if="item.value === 'accountType'">
            {{ cardTypeComput(item.value) }}
          </text>
          <text class="slot-footer-text" v-else>
            {{ state.cardDetail[item.value] || '-' }}
          </text>
        </template>
      </uni-list-item>
    </view>

    <!-- 底部固定按钮 -->
    <view class="bottom-button">
      <button class="next-btn" @click="state.showPasswordVerify = true">解 绑</button>
    </view>
    <!-- 支付密码验证弹窗 -->
    <PayPasswordVerify v-model:visible="state.showPasswordVerify" title="请验证支付密码" @success="onPasswordVerifySuccess" />
    <!-- :verify-function="verifyPayPassword"  @success="onPasswordVerifySuccess"  @failed="onPasswordVerifyFailed" @cancel="onPasswordVerifyCancel" -->
  </view>
</template>

<script setup>
import PayPasswordVerify from "../components/PayPasswordVerify.vue";
import NavBar from "../components/navBar.vue"
import { encrypt } from "@/utils/rsa";
import useStore from "@/store"
import { computed } from "vue";
const { useSmartCard } = useStore()
const _returnParams = computed(() => useSmartCard.getSmart.returnParams)
const _returnRoute = computed(() => useSmartCard.getSmart.returnRoute)
const unbindList = [
  { label: '姓名', value: 'accountName' },
  { label: '身份证号码', value: 'certNo' },
  { label: '开户银行', value: 'bankName' },
  { label: '银行卡类型', value: 'accountType' },
  { label: '银行卡号', value: 'accountNo' },
  { label: '手机号码', value: 'mobile' },
]
const state = reactive({
  showPasswordVerify: false,
  password: "",
  cardDetail: {
    accountName: "",
    certNo: "",
    bankName: "",
    accountType: "",
    accountNo: "",
    mobile: "",
  },
});

// 银行卡类型选项
const cardTypeOptions = [{ value: "DEBITCARD", text: "储蓄卡" }]
const cardTypeComput = computed(() => {
  return (item) => cardTypeOptions.find((i) => i.value === state.cardDetail[item])?.text
});
const unBindOk = () => {
  uni.showModal({
    content: "确定解绑该银行卡吗？解绑后不能用此银行卡进行充值、缴费等",
    cancelText: "否",
    confirmText: "是",
    confirmColor: "#00B781",
    cancelColor: "#00B781",

    success: (res) => {
      if (res.confirm) {
        const { relatedId, personId } = _returnParams.value
        const params = {
          paramEncipher: encrypt(JSON.stringify({
            relatedId, personId,
            password: state.password
          })),
        }
        http.post("/unicard/app/bocom/bank-card/card-unbind", params).then(() => {
          uni.showToast({
            title: "解绑成功",
            icon: "none",
          });
          clickLeft()
        });
      }
    },
  });
};
// 支付密码验证函数
const onPasswordVerifySuccess = (password) => {
  state.password = password
  state.showPasswordVerify = false
  unBindOk()
};
// 返回处理
function clickLeft() {
  // 获取最后一个路由
  const _route = _returnRoute.value.pop()
  navigateTo({ url: _route })
}
// 获取银行卡详情
const initPage = () => {
  const { relatedId, personId } = _returnParams.value
  const parasm = { personId, relatedId }
  http.post("/unicard/app/bocom/bank-card/card-detail", parasm).then(({ data }) => {
    state.cardDetail = data;
  });
};
onMounted(() => {
  initPage();
});
</script>

<style lang="scss" scoped>
.unbind-container {
  display: flex;
  flex-direction: column;
  position: relative;
  background: #f9faf9;
  height: calc(100vh - var(--window-top));

  .slot-footer-text {
    font-weight: 400;
    font-size: 28rpx;
    color: #666666;
  }

  .content {
    flex: 1;
    margin-top: 20rpx;

    .slot-box {
      .slot-icon {
        color: #f5222d;
      }

      .slot-text {
        font-weight: 400;
        font-size: 28rpx;
        color: #333333;
      }
    }
  }

  .bottom-button {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 30rpx;
    // padding-bottom: calc(30rpx + env(safe-area-inset-bottom));
    background-color: #ffffff;
    border-top: 1rpx solid #f0f0f0;

    .next-btn {
      width: 100%;
      height: 92rpx;
      border-radius: 10rpx;
      color: #ffffff;
      background-color: #f5222d;
      border: none;
      outline: none;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease;
      font-weight: 400;
      font-size: 32rpx;
    }
  }
}
</style>
