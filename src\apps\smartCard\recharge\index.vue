<template>
    <!-- 一卡通充值 recharge-->
    <view class="recharge">
        <NavBar title="一卡通充值" :clickLeft="clickLeft" />
        <view class="content">
            <template v-for="item in cardForm" :key="item.key">
                <view v-if="item.key == 'payAmount'">
                    <uni-list-item :title="item.name" />
                    <view v-if="item.key == 'payAmount'" class="payAmount">
                        <view class="reset-easyinput_amount">
                            <text>￥</text>
                            <view class="reset-easyinput" @tap="changeAmount">
                                {{ state.form.payAmount }}
                                <text v-if="!state.form.payAmount" class="placeholder">请输入充值金</text>
                            </view>
                        </view>

                        <view class="btns">
                            <button v-for="it in moneys" :key="it.value" class="mini-btn"
                                :class="{ active: state.form.payAmount == it.value }" type="primary" size="mini"
                                @click="state.form.payAmount = it.value">{{ it.name }}</button>
                        </view>
                    </view>
                </view>
                <uni-list-item v-else :title="item.name">
                    <template v-slot:footer>
                        {{ state.form[item.key] }}
                    </template>
                </uni-list-item>
            </template>
        </view>
        <view class="footer">
            <button class="mini-btn" type="primary" :disabled="!state.form.payAmount" @click="handleSave">确认</button>
        </view>
        <uv-keyboard ref="keyboard" mode="number" @change="changeKeyboard" @confirm="confirmKeyboard"
            @backspace="backspaceKeyboard" @close="closeKeyboard"></uv-keyboard>
    </view>
</template>

<script setup>
import NavBar from "../components/navBar.vue"
import useStore from "@/store"
const { useSmartCard } = useStore()
const _returnParams = computed(() => useSmartCard.getSmart.returnParams)
const _returnRoute = computed(() => useSmartCard.getSmart.returnRoute)
const keyboard = ref(null)

const moneys = [
    { name: "20元", value: 20 },
    { name: "50元", value: 50 },
    { name: "100元", value: 100 },
    { name: "200元", value: 200 }
]
const cardForm = [
    { name: "姓名", key: "name" },
    { name: "卡号", key: "cardNo" },
    { name: "当前余额", key: "balance" },
    { name: "充值金额", key: "payAmount" }
]

const state = reactive({
    form: {
        name: "",
        cardNo: "",
        cashPledge: "",
        personId: "",
        payAmount: ""
    },
    studentName: "",
    oldPayAmount: "",
})
// 跳到订单支付页
const handleSave = () => {
    useSmartCard.setReturnParams({
        ..._returnParams.value,
        payAmount: state.form.payAmount,
        crossRoute: '/apps/smartCard/recharge/orderPayment' // 越级跳转
    })
    useSmartCard.setReturnRoute([..._returnRoute.value, '/apps/smartCard/recharge/index'])
    navigateTo({
        url: "/apps/smartCard/recharge/orderPayment",
    })
}
const initPage = () => {
    let params = { studentId: _returnParams.value.studentId || _returnParams.value.personId }
    http.post("/unicard/app/person/info", params).then(({ data }) => {
        state.form = data
        state.form.payAmount = ""
    })
}
// 打开键盘
const changeAmount = () => {
    state.oldPayAmount = JSON.parse(JSON.stringify(state.form.payAmount))
    if (Number(state.oldPayAmount)) {
        state.oldPayAmount = state.oldPayAmount + ""
    }
    keyboard.value.open()
}
const changeKeyboard = (evt) => {
    state.form.payAmount += evt
}
// 确定提交
const confirmKeyboard = () => {
    state.oldPayAmount = state.form.payAmount
}
// 取消
const closeKeyboard = () => {
    if (state.oldPayAmount != state.form.payAmount) {
        state.form.payAmount = state.oldPayAmount
    }
    keyboard.value.close()
}
// 退格键
const backspaceKeyboard = () => {
    if (Number(state.form.payAmount)) {
        state.form.payAmount = state.form.payAmount + ""
    }
    state.form.payAmount = state.form.payAmount.slice(0, state.form.payAmount.length - 1)
}

onLoad(() => {
    initPage()
})
function clickLeft() {
    const _route = _returnRoute.value.pop()
    navigateTo({
        url: _route
    })
    // if (_returnParams.value.studentId) {
    //     navigateTo({
    //         url: "/apps/smartCard/cardDetail/index",
    //         // query: {
    //         //     personId: _returnParams.value.personId,
    //         //     studentId: _returnParams.value.studentId
    //         // }
    //     })
    // } else {
    //     navigateTo({
    //         url: "/apps/smartCard/index"
    //     })
    // }
}
</script>

<style lang="scss" scoped>
.recharge {
    background: $uni-bg-color-grey;
    height: 100vh;

    .content {
        background: $uni-bg-color;
        padding: 0 30rpx;

        .slot-image {
            width: 80rpx;
            height: 80rpx;
            border-radius: 50%;
        }

        .slot-text {
            font-weight: 400;
            font-size: 28rpx;
            color: $uni-text-color-grey;
        }

        :deep(.uni-icons) {
            padding-left: 0;
        }

        :deep(.uni-list-item__container) {
            padding: 34rpx 0;
        }

        .payAmount {
            padding-bottom: 20rpx;

            .reset-easyinput_amount {
                display: flex;
                align-items: center;
                margin-bottom: 28rpx;
                padding-bottom: 30rpx;
                border-bottom: 1rpx solid #e5e5e5;

                .reset-easyinput {
                    flex: 1;

                    .placeholder {
                        color: $uni-text-color-grey;
                        font-size: 28rpx;
                    }
                }
            }

            .btns {
                display: flex;
                justify-content: space-between;
                margin-bottom: 28rpx;

                .mini-btn {
                    flex: 1;
                    color: $uni-text-color;
                    background: #f6f6f6;
                    border: 1rpx solid #f6f6f6;

                    &:not(:first-child),
                    &:not(:last-child) {
                        margin: 0 10rpx;
                    }

                    &.active {
                        background: rgba(0, 209, 144, 0.08);
                        border-color: var(--primary-color);
                        color: var(--primary-color);
                    }
                }
            }
        }
    }

    .footer {
        padding: 20px 32rpx;
        background: $uni-bg-color;
        display: flex;
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;

        .mini-btn {
            background-color: var(--primary-color);
            flex: 1;
        }
    }
}
</style>
