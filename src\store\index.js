import useUserStore from "./user"
import useLocalStore from "./local"
import useSystemStore from "./system"
import useHomeStore from "./home"
import useVoteStore from "./vote"
import useCollectTable from "./collectTable"
import useOfficialDocs from "./officialDocs"
import useSmartCard from "./smartCard"

export default function useStore() {
    return {
        user: useUserStore(),
        local: useLocalStore(),
        system: useSystemStore(),
        home: useHomeStore(),
        vote: useVoteStore(),
        collectTable: useCollectTable(),
        officialDocs: useOfficialDocs(),
        useSmartCard: useSmartCard(),
    }
}
