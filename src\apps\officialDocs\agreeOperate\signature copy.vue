<template>
    <view class='signature'>
        <NavBar title="添加签章" :clickLeft="clickLeft" />
        <view class='content' ref="contentRef" @scroll.passive="handleScroll">
            <view class="qz-container">
                <view class="qz-item" v-for="(item, idx) in state.previewUrls" :key="idx" :id="`page-${idx}`">
                    <img :src="item" alt="" class="a4-page">
                </view>
                <template v-if="state.checkSeal.src">
                    <VueDragResize :isActive="state.checkSeal.active" :parentLimitation="false" :is-resizable="true"
                        :w="state.checkSeal.w" :h="state.checkSeal.h" :x="state.checkSeal.x" :y="state.checkSeal.y"
                        :minw="50" :minh="50" @resizing="handleResizeStop" @dragging="handleDragsStop"
                        @activated="handleActivated" @deactivated="handleDeactivated">
                        <img :style="{ width: `100%`, height: `100%`, cursor: 'grab' }" :src="state.checkSeal.src"
                            alt="签章">
                    </VueDragResize>
                </template>
            </view>

        </view>
        <view class="footer">
            <button class="mini-btn" type="default" size="mini" @click="childPopup.open()">选择印章</button>
            <button class="mini-btn" :class="{ disabled: !state.checkSeal.src }" type="primary" size="mini"
                :disabled="!state.checkSeal.src" :loading="state.loading" @click="handerSubmitSing">确认提交</button>
        </view>
        <uni-popup ref="childPopup" type="bottom" background-color="#fff" borderRadius="20rpx 20rpx 0 0">
            <view class="child-switch">
                <view class="handle">
                    <text class="title">选择印章</text>
                    <uni-icons class="close" type="closeempty" size="16" @click="childPopup.close()"></uni-icons>
                </view>
                <view class="child-list">
                    <radio-group @change="radioChange">
                        <label class="uni-list-cell" v-for="item in sealList" :key="item.id">
                            <view>{{ item.sealName }}</view>
                            <view>
                                <radio :value="item.id" :checked="item.id === state.sealId" color="#00b781" />
                            </view>
                        </label>
                    </radio-group>
                </view>
            </view>
        </uni-popup>
    </view>
</template>

<script setup>
import NavBar from "../components/navBar.vue"
import VueDragResize from 'vue-drag-resize/src'
import { computed, nextTick, onMounted, onUnmounted, reactive, ref } from 'vue'
import useStore from "@/store"
const { officialDocs } = useStore()

const contentRef = ref(null)
const childPopup = ref(null)
const sealList = ref([])

// A4标准尺寸(毫米)
const A4_WIDTH_MM = 210
const A4_HEIGHT_MM = 297
// A4宽高比
const A4_RATIO = A4_HEIGHT_MM / A4_WIDTH_MM

const state = reactive({
    id: '',
    operate: "",
    type: '',
    showType: '',
    previewfileId: '', // 正文id
    previewUrls: [],
    pageDimensions: [], // 存储每页的实际尺寸
    checkSeal: {
        active: false,
        w: 100,
        h: 100,
        x: 20,
        y: 20,
        src: '',
        pageIndex: 0, // 当前所在页码
        mmPosition: { x: 0, y: 0, w: 0, h: 0 } // 毫米单位的位置信息
    },
    sealId: '', // 印章id
    documentId: '', // 文档id
    loading: false,

    scrollTop: 0, // 新增滚动位置记录
    isInitialized: false // 新增初始化标志
})
// 新增滚动处理
function handleScroll(e) {
    state.scrollTop = e.detail.scrollTop
}
// 计算A4在屏幕中的实际尺寸
function calculatePageDimensions() {
    return new Promise((resolve) => {
        //  #ifdef MP-WEIXIN 
        const query = uni.createSelectorQuery().in(this);
        query.selectAll('.qz-item').boundingClientRect(rects => {
            const containerQuery = uni.createSelectorQuery().in(this);
            containerQuery.select('.content').boundingClientRect(containerRect => {
                const dimensions = []
                let cumulativeHeight = 0

                rects.forEach(rect => {
                    const width = rect.width
                    const height = width * A4_RATIO
                    dimensions.push({
                        width,
                        height,
                        startY: cumulativeHeight,
                        endY: cumulativeHeight + height
                    })
                    cumulativeHeight += height + 20 // 加上间隙
                })

                state.pageDimensions = dimensions
                resolve(dimensions)
            }).exec()
        }).exec()
        //  #endif
        //  #ifndef MP-WEIXIN 
        const container = contentRef.value?.$el || document.querySelector('.qz-container')
        if (!container) return []

        const pages = document.querySelectorAll('.qz-item')
        const dimensions = []
        let cumulativeHeight = 0
        pages.forEach(page => {
            const width = page.offsetWidth
            const height = width * A4_RATIO
            dimensions.push({
                width, height,
                startY: cumulativeHeight,
                endY: cumulativeHeight + height
            })
            cumulativeHeight += height + 20 // 加上间隙
        })
        state.pageDimensions = dimensions
        return dimensions
        //  #endif
    })
}

// 毫米转像素
function mmToPx(mm, pageWidth) {
    return (mm / A4_WIDTH_MM) * pageWidth
}

// 像素转毫米
function pxToMm(px, pageWidth) {
    return (px / pageWidth) * A4_WIDTH_MM
}

// 更新签章位置信息（毫米单位）
function updateSealMmPosition() {
    const { pageIndex, x, y, w, h } = state.checkSeal
    const pageDim = state.pageDimensions[pageIndex]
    if (!pageDim) return

    state.checkSeal.mmPosition = {
        x: pxToMm(x, pageDim.width),
        y: pxToMm(y, pageDim.height),
        w: pxToMm(w, pageDim.width),
        h: pxToMm(h, pageDim.height)
    }
    // console.log(state.checkSeal.mmPosition);
}

// 处理拖动停止
function handleDragsStop(newRect) {
    const { top: y, left: x } = newRect
    state.checkSeal.x = x
    state.checkSeal.y = y
    console.log({ y });

    // 更新当前页码
    updateCurrentPageIndex(y)
    updateSealMmPosition()
}

// 处理缩放停止
function handleResizeStop(newRect) {
    const { top: y, left: x, width: w, height: h } = newRect
    state.checkSeal.x = x
    state.checkSeal.y = y
    state.checkSeal.w = w
    state.checkSeal.h = h

    updateSealMmPosition()
}

// 激活签章
function handleActivated() {
    state.checkSeal.active = true
}

// 取消激活签章
function handleDeactivated() {
    state.checkSeal.active = false
}

// 更新当前页码
function updateCurrentPageIndex(y) {
    for (let i = 0; i < state.pageDimensions.length; i++) {
        const pageHeight = state.pageDimensions[i].height
        if (y < (i + 1) * pageHeight) {
            state.checkSeal.pageIndex = i
            break
        }
    }
}

// 选择印章
const radioChange = (evt) => {
    state.sealId = evt.detail.value
    const item = sealList.value.find(i => i.id == state.sealId)
    if (!item) return

    // 初始化签章位置
    initSealPosition(item.sealUrl)
    childPopup.value.close()
}
const handleFallbackSealPosition = async (sealUrl, type) => {
    let _sealUrl = sealUrl
    try {
        // 先下载到本地
        if (type == 'weixin') {
            const _res = await uni.downloadFile({
                url: _sealUrl
            })
            _sealUrl = _res.tempFilePath
        }
        // 使用本地路径
        if (state.pageDimensions.length) {
            const firstPage = state.pageDimensions[0]
            const scale = 0.15
            state.checkSeal.w = firstPage.width * scale
            state.checkSeal.h = state.checkSeal.w
            state.checkSeal.x = firstPage.width - state.checkSeal.w - 20
            state.checkSeal.y = firstPage.height - state.checkSeal.h - 20
            state.checkSeal.pageIndex = 0
            state.checkSeal.src = _sealUrl
            updateSealMmPosition()
        }
    } catch (err) {
        console.error('图片处理失败:', err)
        handleFallbackSealPosition(_sealUrl)
    }
}
// 初始化签章位置
function initSealPosition(sealUrl) {
    //  #ifdef MP-WEIXIN
    uni.getImageInfo({
        src: sealUrl,
        success: (res) => {
            handleFallbackSealPosition(sealUrl, 'weixin')
        },
        fail: (err) => {
            console.error('获取图片信息失败', err)
            // 即使获取失败也设置图片源
            state.checkSeal.src = sealUrl
        }
    })
    //  #endif
    //  #ifndef MP-WEIXIN
    const img = new Image()
    img.src = sealUrl
    img.onload = () => {
        handleFallbackSealPosition(sealUrl, '')
    }
    img.onerror = () => {
        state.checkSeal.src = sealUrl
    }
    //  #endif

}

// 获取正文
const initPage = async () => {
    const { data } = await http.post("/cloud/official-doc/document/preview", { documentId: state.id })
    state.previewfileId = data.fileId
    state.previewUrls = data.previewUrls
    await nextTick()
    await calculatePageDimensions()
    state.isInitialized = true
}

// 获取印章列表
const getSingList = async () => {
    const { data } = await http.post("/cloud/official-doc/seal/enabledList")
    sealList.value = data
}

// 恢复签章位置方法
function restoreSealPosition() {
    if (!state.isInitialized || !_formListData.value?.imageBox) return
    const { imageBox, pageIndex } = _formListData.value
    const pageDim = state.pageDimensions[pageIndex]
    if (!pageDim) return

    // 计算签章在屏幕中的位置
    const sealHeight = mmToPx(imageBox.h, pageDim.width)
    const sealY = mmToPx(imageBox.y, pageDim.width) + pageDim.startY

    // 滚动到签章位置（让签章位于屏幕中央）
    const targetScrollTop = sealY - (window.innerHeight / 2 - sealHeight / 2)
    contentRef.value?.$el?.scrollTo?.({
        top: targetScrollTop,
        behavior: 'smooth'
    })

    // 设置签章位置
    state.checkSeal.w = mmToPx(imageBox.w, pageDim.width)
    state.checkSeal.h = sealHeight
    state.checkSeal.x = mmToPx(imageBox.x, pageDim.width)
    state.checkSeal.y = mmToPx(imageBox.y, pageDim.height)
    state.checkSeal.pageIndex = pageIndex
    state.checkSeal.mmPosition = {
        ...imageBox,
    }

    // 找到对应的印章
    const sealId = _formListData.value.sealId
    const item = sealList.value.find(i => i.id == sealId)
    if (item) {
        state.sealId = sealId
        state.checkSeal.src = item.sealUrl
    }
}
// 确认提交印章
const handerSubmitSing = async () => {
    const { id, sealId, checkSeal, previewfileId } = state
    const pages = document.querySelectorAll('.qz-item')
    const _width = pages[checkSeal.pageIndex].offsetWidth
    const _height = pages[checkSeal.pageIndex].offsetHeight
    const params = {
        sealId,
        documentId: id,
        fileId: previewfileId,
        pageIndex: checkSeal.pageIndex,
        pageSize: {
            width: _width || A4_WIDTH_MM,
            height: _height || A4_HEIGHT_MM
        },
        imageBox: {
            x: checkSeal.x,
            y: checkSeal.pageIndex ? checkSeal.y - ((_height + 11) * checkSeal.pageIndex) : checkSeal.y,
            width: checkSeal.w,
            height: checkSeal.h
        }
    }
    state.loading = true
    try {
        console.log({ params });
        const res = await http.post("/cloud/official-doc/document/apply-seal", params)
        // 更新store中的数据
        officialDocs.setSubmitFormListKey('fileId', res.data.fileId)
        officialDocs.setSubmitFormListKey('fileUrl', res.data.fileUrl)
        officialDocs.setSubmitFormListKey('sealId', sealId)
        officialDocs.setSubmitFormListKey('pageIndex', params.pageIndex)
        officialDocs.setSubmitFormListKey('imageBox', checkSeal.mmPosition)
        officialDocs.setSubmitFormListKey('scrollPosition', params.scrollPosition) // 保存滚动位置

        uni.showToast({
            title: res.message,
            icon: "success"
        })
        clickLeft()
    } finally {
        state.loading = false
    }
}


// 监听窗口大小变化
const handleResize = () => {
    state.pageDimensions = calculatePageDimensions()
}
const _formListData = computed(() => officialDocs.submitFormData)

onMounted(async () => {
    uni.showToast({
        title: '',
        icon: "loading"
    })
    // #ifndef MP-WEIXIN 
    window.addEventListener('resize', handleResize)
    // #endif
    await getSingList()
    await initPage()
    uni.hideToast()
    // initExistingSeal()

    // 如果有保存的位置，滚动到对应位置
    restoreSealPosition()

})

onUnmounted(() => {
    // #ifndef MP-WEIXIN 
    window.removeEventListener('resize', handleResize)
    // #endif
})

function clickLeft() {
    const { type, operate, showType, id } = state
    navigateTo({
        url: "/apps/officialDocs/agreeOperate/index",
        query: {
            id,
            type,
            operate,
            showType,
        },
    })
}

onLoad(async (item) => {
    Object.keys(item).forEach((key) => {
        item[key] = decodeURIComponent(item[key])
    })
    state.operate = item.operate || ''
    state.id = item.id
    state.type = item.type
    state.showType = item.showType
})
</script>

<style lang='scss' scoped>
$bag: #F7F7F7;

.signature {
    width: 100vw;
    height: 100vh;
    background: $bag;
    display: flex;
    flex-direction: column;

    .content {
        flex: 1;
        margin: 30rpx;
        overflow: auto;

        .qz-container {
            display: flex;
            flex-direction: column;
            gap: 20rpx;
            position: relative;

            .qz-item {
                background: #fff;
                box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);

                .a4-page {
                    width: 100%;
                    height: auto;
                    display: block;
                    aspect-ratio: 210/297; // 保持A4比例
                }
            }
        }
    }

    .footer {
        position: fixed;
        left: 0;
        bottom: 0;
        box-sizing: border-box;
        background: $uni-text-color-inverse;
        width: 100vw;
        padding: 23rpx 30rpx 47rpx;
        text-align: right;

        &.download {
            display: flex;
            justify-content: space-evenly;

            .btn {
                text-align: center;
                font-weight: 400;
                font-size: 24rpx;
                color: #333333;
            }
        }

        .mini-btn {
            margin: 0 10rpx;
            border: 1px solid $uni-text-color-grey;
            background-color: $uni-text-color-inverse;
            padding-top: 6rpx;
            padding-bottom: 6rpx;

            &[type="primary"] {
                background-color: $uni-color-primary;
                border: 1px solid $uni-color-primary
            }

            &.disabled {
                border: 1px solid #c1c1c1;
            }
        }
    }

    .child-switch {
        min-height: 400rpx;
        padding-bottom: env(safe-area-inset-bottom);

        .handle {
            text-align: center;
            position: relative;
            height: 80rpx;
            line-height: 80rpx;
            font-weight: bold;
            border-bottom: 1rpx solid #f2f2f2;

            .close {
                position: absolute;
                right: 20rpx;
                top: 50%;
                transform: translateY(-50%);
            }
        }

        .child-list {
            max-height: 60vh;
            overflow-y: auto;

            .uni-list-cell {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 20rpx 30rpx;
                border-bottom: 1rpx solid #f2f2f2;
            }
        }
    }
}
</style>