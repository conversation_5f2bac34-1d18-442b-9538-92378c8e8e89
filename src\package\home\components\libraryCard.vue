<template>
    <view class="library_card" v-if="!!libId">
        <view class="library_type_list" v-for="type in libCardList" :key="type.key">
            <view class="title_box">
                <text class="title">{{ type.title }}</text>
                <view @click="goDetail(type)" class="title_right">更多图书<uni-icons type="right"></uni-icons></view>
            </view>
            <view class="books_list" v-if="type.list && type.list.length">
                <view class="books_item" v-for="(item, index) in type.list" :key="index + 'book'">
                    <image :src="item.url || 'https://file.1d1j.cn/library-miniprogram/bookDefaultImg.png'" class="books_img" alt="" />
                    <text class="books_title">{{ item.title }}</text>
                </view>
            </view>
            <div class="nodata" v-else>
                <yd-empty text="暂无数据" />
            </div>
        </view>
    </view>
</template>

<script setup>
const libId = ref(null)
const newBookList = ref([])
const hotBookList = ref([])

const libCardList = computed(() => {
    return [
        {
            type: "popular",
            key: 1,
            title: "热门图书",
            list: hotBookList.value
        },
        {
            type: "newBook",
            key: 2,
            title: "新书推荐",
            list: newBookList.value
        }
    ]
})

// 新书推荐
function getNewBookRecommend() {
    // 获取新书推荐
    http.post("/rfid/mobile/rank/new-book-recommend", {
        libraryId: libId.value,
        pageSize: 8,
        pageNo: 1
    }).then((res) => {
        newBookList.value = res.data
    })
}

// 热门借阅排行
function getHotBookList() {
    http.post("/rfid/mobile/rank/hot-circulation-rank", {
        libraryId: libId.value,
        pageSize: 8,
        pageNo: 1
    }).then((res) => {
        hotBookList.value = res.data
    })
}

function getLibList() {
    http.post("/rfid/mobile/library/list-libraries").then((res) => {
        libId.value = res.data[0]?.id
        if (libId.value) {
            getNewBookRecommend()
            getHotBookList()
        }
    })
}

function goDetail(item) {
    navigateTo({
        url: "/apps/library/index",
        query: {
            type: item.type
        }
    })
}
</script>

<style lang="scss" scoped>
.library_card {
    background: #f9faf9;
    padding: 30rpx;

    .library_type_list {
        margin-bottom: 24rpx;

        .title_box {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20rpx;

            .title {
                font-weight: 600;
                font-size: 28rpx;
                color: #333333;
                line-height: 40r;
                text-align: left;
            }

            .title_right {
                display: flex;
                align-items: center;
                font-weight: 400;
                font-size: 24rpx;
                color: #333333;
            }
        }

        .books_list {
            padding: 30rpx 36rpx;
            background: #fff;
            border-radius: 16rpx;
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 10rpx;

            .books_item {
                display: flex;
                flex-direction: column;
                align-items: center;

                .books_img {
                    width: 122rpx;
                    height: 158rpx;
                    margin-bottom: 18rpx;
                }

                .books_title {
                    font-weight: 400;
                    font-size: 22rpx;
                    color: #333333;
                    display: -webkit-box;
                    -webkit-line-clamp: 2;
                    -webkit-box-orient: vertical;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    word-break: break-all;
                }
            }
        }
        .nodata {
            padding: 30px 36px;
            background: #fff;
        }
    }
}
</style>
