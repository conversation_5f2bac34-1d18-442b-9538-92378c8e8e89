<!-- authentication 身份验证-->
<template>
    <view class="authentication">
        <NavBar title="身份验证" :clickLeft="clickLeft" />
        <view class="authentication-item">
            <input type="number" placeholder="请输入手机号" :maxlength="11" v-model="state.form.phone">
        </view>
        <view class="authentication-item">
            <input type="number" placeholder="请输入验证码" :maxlength="6" v-model="state.form.smsCode">

            <text class="authentication-placeholder" v-if="codeNum">
                {{ codeNum }}s 重新获取
            </text>
            <text v-else class="authentication-placeholder" :class="{ active: state.form.phone }" @click="getCode">
                获取验证码
                <!-- <button class="sms-btn" @click="getCode">
                    发送
                </button> -->
            </text>
        </view>
        <!-- 底部固定按钮 -->
        <view class="bottom-fixed">
            <!-- :disabled="!state.form.smsCode" -->
            <button class="confirm-btn" :class="{ disabled: !state.form.smsCode }" @click="handleSubmit">
                下一步
            </button>
        </view>
    </view>
</template>

<script setup>
import NavBar from "../components/navBar.vue"
import useStore from "@/store"
const { useSmartCard } = useStore()
const _returnParams = computed(() => useSmartCard.getSmart.returnParams)
const _returnRoute = computed(() => useSmartCard.getSmart.returnRoute)
const codeNum = ref(0)
const state = reactive({
    form: {
        phone: '',
        smsCode: ''
    }
})
// 获取验证码
const getCode = () => {
    if (state.form.phone) {
        // 倒计时60秒，60s后codeNum.value 为0
        http.post('/unicard/app/sms/otc/send', { phone: state.form.phone }).then(({ data }) => {
            codeNum.value = 60
            const time = setInterval(() => {
                codeNum.value--
                if (codeNum.value == 0) {
                    clearInterval(time)
                    codeNum.value = 0
                }
            }, 1000)
        })
    }
}
// 下一步  校验验证码
const handleSubmit = () => {
    http.post('/unicard/app/sms/otc/verify', state.form).then(({ data }) => {
        if (data) {
            useSmartCard.setReturnParams({
                ..._returnParams.value,
                crossRoute: '/apps/smartCard/cardDetail/index'
            })
            useSmartCard.setReturnRoute([..._returnRoute.value, '/apps/smartCard/bankCard/authentication'])
            navigateTo({
                url: "/apps/smartCard/bankCard/payPasswordSetting",
            });
        }
    })
}
// 页面挂载时的初始化
onMounted(() => {
    state.form.phone = _returnParams.value.loginPhone
    console.log("银行卡列表页面已挂载");
});

/**
 * 返回处理
 */
function clickLeft() {
    // 获取最后一个路由
    const _route = _returnRoute.value.pop()
    navigateTo({ url: _route })
}
</script>

<style lang="scss" scoped>
.authentication {
    .authentication-item {
        border-bottom: 1rpx solid #D9D9D9;
        padding: 20rpx 0;
        margin: 0 30rpx;
        display: flex;
        justify-content: space-between;

        .authentication-text {
            font-weight: 400;
            font-size: 30rpx;
            color: #000000;
        }

        .authentication-placeholder {
            color: rgba(0, 0, 0, 0.25);

            &.active {
                color: #00b781;
            }
        }

        // .sms {
        //     display: flex;

        //     .sms-btn {
        //         width: 120rpx;
        //         height: 60rpx;
        //         background-color: #00b781;
        //         color: #ffffff;
        //         font-size: 24rpx;
        //         border-radius: 8rpx;
        //         border: none;
        //         margin-top: -4px;
        //     }
        // }
    }



    /* 底部固定按钮 */
    .bottom-fixed {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        padding: 32rpx;
        background-color: #ffffff;
        border-top: 1rpx solid #f0f0f0;
        z-index: 100;

        .confirm-btn {
            width: 100%;
            height: 88rpx;
            background-color: #00b781;
            color: #ffffff;
            border: none;
            font-weight: 400;
            font-size: 32rpx;

            &.disabled {
                background-color: #cccccc;
                color: #999999;
            }

            &:active:not(.disabled) {
                background-color: #00b781;
            }
        }
    }
}
</style>
