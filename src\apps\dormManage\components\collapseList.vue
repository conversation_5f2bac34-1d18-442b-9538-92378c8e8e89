<template>
    <div class="collapse_list">
        <uni-collapse ref="collapse" accordion @change="collapseStatusFn">
            <uni-collapse-item @click="collapseItem(item)" :show-arrow="false" :border="false" v-for="(item, index) in list" :key="index">
                <template #title>
                    <view class="collapse_item">
                        <uni-list :border="false">
                            <uni-list-item>
                                <template #header>
                                    <view class="item_left">
                                        <view :class="{ expand_image: item.collapseStatus == 1, retract_image: item.collapseStatus != 1 }"></view>
                                        <slot name="left" :data="item">
                                            {{ item.bedNum }}
                                        </slot>
                                    </view>
                                </template>
                                <template #body>
                                    <view class="item_body">
                                        <slot name="body" :data="item">
                                            {{ item.studentName }}
                                        </slot>
                                    </view>
                                </template>
                                <template #footer>
                                    <view class="item_right">
                                        <slot name="right" :data="item">
                                            {{ item.classesName }}
                                        </slot>
                                    </view>
                                </template>
                            </uni-list-item>
                        </uni-list>
                    </view>
                </template>
                <slot name="content" :childList="item"></slot>
            </uni-collapse-item>
        </uni-collapse>
    </div>
</template>

<script setup>
import { watch } from "vue"

const collapseStatus = ref(null)
const collapse = ref(null)
const props = defineProps({
    list: {
        type: Array,
        default: []
    }
})

const list = ref([])

watch(
    () => props.list,
    (newVal) => {
        list.value = newVal
    },
    {
        deep: true,
        immediate: true
    }
)

function collapseStatusFn(status) {
    collapseStatus.value = status
}

function collapseItem(item) {
    list.value.forEach((i) => {
        i.collapseStatus = null
    })
    if (collapseStatus.value) {
        item.collapseStatus = "1"
    } else {
        item.collapseStatus = null
    }
}
</script>

<style lang="scss" scoped>
.collapse_list {
    .collapse_item {
        font-size: 28rpx;
        font-weight: 400;
        color: #333333;
        line-height: 40rpx;
        .item_left {
            max-width: 30%;
            min-width: 30%;
            text-align: left;
            padding-left: 10rpx;
            font-weight: 600;
            display: flex;
            align-items: center;
            .retract_image,
            .expand_image {
                height: 28rpx;
                width: 28rpx;
            }
            .retract_image {
                background: url("https://alicdn.1d1j.cn/announcement/20230727/2acd3a4bdc3045e09bf5af1fbfed3b1a.png") no-repeat;
                background-size: contain;
            }
            .expand_image {
                background: url("https://alicdn.1d1j.cn/announcement/20230727/662e6f5e270f400c8748269d74a8d208.png") no-repeat;
                background-size: contain;
            }
        }
        .item_body {
            min-width: 35%;
            max-width: 35%;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        .item_right {
            min-width: 30%;
            max-width: 30%;
            padding-right: 12rpx;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
            word-break: break-all;
            text-align: end;
        }
    }
}
:deep(.uni-list-item__container) {
    justify-content: space-between;
}
</style>
