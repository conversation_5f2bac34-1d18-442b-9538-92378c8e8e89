<template>
    <view class='content' :class="{ active: !_formListData.taskId }">
        <uv-loading-icon style="margin-top: 100px;" v-if="!state.previewUrls.length && state.loading"></uv-loading-icon>
        <view class="qz-item" v-for="(item, idx) in state.previewUrls" :key="idx">
            <image :src="item" class="a4-page" mode="widthFix" />
        </view>
        <yd-empty class="empty" v-if="!state.loading && !state.previewUrls.length">暂无数据</yd-empty>
    </view>
</template>

<script setup>
import useStore from "@/store"
const { officialDocs } = useStore()
const _formListData = computed(() => officialDocs.getFormListData)
const state = reactive({
    id: '',
    previewUrls: [],
    loading: false
})

// 获取正文
const initPage = async () => {
    uni.showToast({
        title: '加载中...',
        icon: "loading"
    })
    state.loading = true
    http.post("/cloud/official-doc/document/preview", { documentId: state.id }).then(({ data }) => {
        state.previewUrls = data.previewUrls
        uni.hideToast()
        state.loading = false
    }).catch(() => {
        uni.hideToast()
        state.loading = false
    })
}

watch(() => _formListData.value, val => {
    if (val.id) {
        state.id = val.id
        initPage()
    }
}, { immediate: true, deep: true })


onLoad((item) => {
    Object.keys(item).forEach((key) => {
        item[key] = decodeURIComponent(item[key])
    })
    state.id = item.id || ""

})
</script>

<style lang='scss' scoped>
.content {
    margin: 20rpx 0 140rpx;
    // background-color: $uni-text-color-inverse;
    overflow: hidden auto;
    height: calc(100vh - 360rpx);

    &.active {
        height: calc(100vh - 210rpx);
        margin-bottom: 0;
    }

    .a4-page,
    .qz-item {
        width: 100vw;
    }

    .qz-item {
        margin: 4rpx 0;
    }

    .empty {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
    }
}
</style>