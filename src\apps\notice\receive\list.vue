<!-- 失物招领 -->
<template>
    <view class="news">
        <view class="news-item" v-for="(item, index) in dataList" :key="index" @click="onConfirm(item)">
            <!-- 我发布的 -->
            <view class="news-item-content">
                <image class="img" v-if="item.coverImg" :src="item.coverImg" />
                <view class="cell-item">
                    <view class="cell-item-hander">
                        <text class="two_ellipsis">{{ item.title }}</text>
                    </view>
                    <view class="cell-item-content">
                        <view class="list-source">
                            {{ item.identityUserName }}发布于<text :class="{ active: !item.status && paramsForm.type == 'publish' }"> {{ item.timerDate }}</text>
                            <text v-if="item.status"> 浏览{{ item.viewUsers }}次 </text>
                        </view>
                    </view>
                </view>
            </view>
            <view class="cell-item-footer" v-if="paramsForm.type == 'publish'">
                <text class="screenDomination" v-if="item.isDominateScreen">霸屏</text>
                <i v-else />
                <uni-list-item :class="{ resetConfimItemActive: !item.status }" :rightText="item.status ? '已发布' : '待发布'" />
            </view>
        </view>
    </view>
</template>

<script setup>
const emit = defineEmits(["complete", "reload"])
const props = defineProps({
    paramsForm: {
        type: Object,
        default: () => {}
    },
    dataList: {
        type: Array,
        default: () => []
    }
})

const dataList = computed(() => props.dataList)

const initPage = (pageNo, pageSize, isAll) => {
    const { type, startTime, endTime } = props.paramsForm
    const params = {
        identifier: "receive",
        keyWords: "",
        startTime,
        endTime,
        pageNo,
        pageSize
    }
    // 我收到的
    let url = "/cloud/mobile/mess/receive/page"
    // 我发布的
    if (type == "publish") {
        url = "/cloud/mobile/mess/publish/page"
    }
    // 所有我发布的
    if (isAll) {
        url = "/app/mobile/mess/publish/page/all"
    }
    http.post(url, params).then(({ data }) => {
        emit("complete", data.list)
    })
}
// 去确认、详情
const onConfirm = (item) => {
    if (!item.isView) {
        emit("reload")
    }
    navigateTo({
        url: "/apps/notice/receive/details",
        query: {
            id: item.id,
            type: props.paramsForm.type,
            receiveUsers: item.receiveUsers,
            contentType: item.contentType,
            messType: item.messType
        }
    })
}

watch(
    () => props.paramsForm,
    () => {
        emit("reload")
    },
    {
        deep: true
    }
)
onShow(() => {
    emit("reload")
})

defineExpose({ initPage })
</script>

<style lang="scss" scoped>
$color6: #666666;
$warning: #faad14;
$red: red;

.news {
    padding: 0rpx 30rpx 30rpx 30rpx;
    .news-item {
        background: $uni-bg-color;
        border-radius: 20rpx;
        margin: 20rpx 0;
        padding: 30rpx;

        .news-item-content {
            display: flex;
            align-items: center;

            .img {
                width: 240rpx;
                height: 180rpx;
                border-radius: 10rpx;
                margin-right: 20rpx;
            }

            .cell-item {
                flex: 1;
                min-height: 100rpx;

                .cell-item-hander {
                    font-size: 30rpx;
                    font-weight: 600;
                }
            }
        }

        .cell-item-content {
            font-size: 24rpx;
            margin: 20rpx 0 0;
            color: $color6;

            .list-source {
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-line-clamp: 1;
                -webkit-box-orient: vertical;

                &:not(:last-child) {
                    margin: 10rpx 0;
                }
            }

            .active {
                color: $warning;
            }
        }

        .cell-item-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 10rpx;
            .screenDomination {
                color: var(--primary-color);
                font-size: 20rpx;
                background-color: #00b7811a;
                border-radius: 10rpx;
                padding: 6rpx;
            }

            :deep(.uni-list-item) {
                .uni-list--border {
                    display: none;
                }

                &.resetConfimItemActive {
                    .uni-list-item__extra span,
                    .uni-icon-wrapper {
                        color: $warning !important;
                    }
                }

                .uni-list-item__extra-text span,
                .uni-icon-wrapper {
                    padding: 0;
                    font-size: 24rpx;
                    color: var(--primary-color);
                }

                .uni-list-item__container {
                    padding: 0;
                }
            }
        }
    }
}
</style>
