/* 一行显示省略号 */
.ellipsis {
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: break-all;
}

/* 两行显示省略号 */
.two_ellipsis {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
}

button:after {
    /* 按钮有个边框 */
    border: none !important;
}

.uni-page-head {
    align-items: center;
}

.uni-page-head__title {
    font-weight: 500 !important;
    font-size: 34rpx !important;
    color: $uni-text-color !important;
    line-height: 48rpx;
    text-align: center;
}

.uni-nav-bar-text {
    font-weight: 500;
    font-size: 32rpx !important;
    color: $uni-text-color !important;
    line-height: 48rpx;
    text-align: center;
}

.uni-picker-action-confirm {
    color: var(--primary-color) !important;
}

/* #ifdef MP-WEIXIN */
::-webkit-scrollbar {
    width: 0;
    height: 0;
    color: transparent;
}

/* #endif */

uni-button[disabled*="true"] {
    background: #ccc !important;
}

button[aria-disabled*="true"] {
    background: #ccc !important;
}

// @media (min-width: 520px) {
//     body{
//         background: #f8f7fb;
//     }
//     uni-page {
// 		width: 414px !important;
// 		overflow-y: auto;
// 		min-width: 300px !important;
//         margin: 0 auto;
//         box-shadow: #999 0 0 12px;
//         background-color: #fff;
// 	}
// 	uni-page-body {
// 		width: 414px !important;
// 	}
// }

.selected-item-active {
    border-bottom: 2px solid var(--primary-color) !important;
}

.uni-scroll-view-content {
    .check {
        border: 2px solid var(--primary-color) !important;
        border-left: 0 !important;
        border-top: 0 !important;
    }
}

.uni-button-color {
    color: var(--primary-color) !important;
}

/* #ifdef MP-WEIXIN */
.uni-data-pickerview {
    .check {
        border: 2px solid var(--primary-color) !important;
        border-left: 0 !important;
        border-top: 0 !important;
    }
}
.uni-date {
    .icon-calendar {
        display: none !important;
    }
    .uni-date__x-input {
        flex: none !important;
    }
}
// 稿纸样式 start
.official_docs {
    .reset-input {
        .uni-easyinput {
            text-align: right !important;
            .uni-easyinput__content {
                background-color: $uni-text-color-inverse !important;
            }
        }
    }
    .form-item {
        position: relative;
        border-bottom: 1rpx solid $uni-border-color;
        margin: 0;
        padding: 16rpx 0rpx;
        .uni-forms-item {
            margin: 0;

            .uni-easyinput__content-input {
                color: #999 !important;
                padding: 0 28rpx 0 0 !important;
            }
        }
        &.date .uni-date-x {
            flex: 1 !important;
            justify-content: flex-end !important;
            padding-right: 24rpx;
        }

        &.textarea {
            .text-count {
                color: #999;
                text-align: right !important;
            }
            .reset-input {
                .uni-easyinput {
                    text-align: left !important;
                    .uni-easyinput__content {
                        background-color: #f6f6f6 !important;
                    }
                }
            }
            .uni-forms-item {
                display: block !important;
            }
        }
        .right-icon {
            position: absolute;
            right: -4rpx;
            top: 30rpx;
            color: #999 !important;
        }
    }
    .official_docs_manage {
        .header {
            .search-easyinput {
                :deep(.uni-easyinput) {
                    border-bottom: 23rpx solid #f0f2f5;
                    .uni-easyinput__content {
                        margin: 10px 20rpx;
                        background: #f0f2f5;
                        width: 94vw;
                        border-radius: 50rpx;
                    }
                }
            }
        }
        .tabs {
            border-bottom: 1px solid #f0f2f5;
        }
    }
}
// 稿纸样式 end
.notic {
    :deep(.uni-select) {
        border: none !important;
    }
}
.official_docs {
    .right-icon.active {
        width: 200rpx !important;
        text-align: right !important;
        z-index: 99;
    }
}
/* #endif */
