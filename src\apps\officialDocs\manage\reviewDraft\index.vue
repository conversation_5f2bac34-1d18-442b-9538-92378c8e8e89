<template>
    <view class='review-draft'>
        <NavBar :title='barText' :clickLeft="clickLeft"></NavBar>
        <Tabs :list="tabs" @change="changeTabs" v-model:current="state.review"></Tabs>
        <OfficialDocs v-if="state.review == 'officialDocs'" />
        <Content v-else-if="state.review == 'content'" />
        <Attachment v-else-if="state.review == 'attachment'" />
        <Log v-else-if="state.review == 'log'" />
        <view class="footer" v-if="state.formListData.taskId">
            <button v-if="editables.includes(state.formListData.status)" class="mini-btn" type="primary" size="mini"
                @click="handleSubmitUpadte">提交</button>
            <template v-else>
                <button class="mini-btn" type="default" size="mini" @click="handleSubmit('back')">回 退</button>
                <button class="mini-btn" type="primary" size="mini" @click="handleSubmit('agre')">{{ btnText }}</button>
            </template>
        </view>
    </view>
</template>

<script setup>
import { onMounted, reactive } from 'vue'
import Tabs from '../../components/tabs.vue'
import NavBar from '../../components/navBar.vue'
import OfficialDocs from './officialDocs.vue'
import Content from './content.vue'
import Attachment from './attachment.vue'
import Log from './log.vue'
import useStore from "@/store"
const { officialDocs } = useStore()
const _formListData = computed(() => officialDocs.getFormListData)

const barText = computed(() => {
    if (officialDocs.getFormListData?.statusCode) {
        return officialDocs.getFormListData.statusCode
    }
    return ''
})
const btnText = computed(() => {
    const _status = officialDocs.getFormListData.status
    const obj = {
        "TAO_HONG": "套 红",
        "QIAN_ZHANG": "签 章",
        "FEN_FA": "分 发",
        "BAN_LI": "办 理",
        "CHUAN_YUE": "传 阅",
        "YUE_DU": "阅 读",
        "BAN_JIE": "办 结",
        "GUI_DANG": "归 档",
        "CHENG_BAN": "承 办",
    }
    return obj[_status] || '同 意'
})
const editables = ['NI_GAO', 'DENG_JI']

const tabs = [
    { name: '稿纸', key: 'officialDocs' },
    { name: '正文', key: 'content' },
    { name: '附件', key: 'attachment' },
    { name: '日志', key: 'log' },
]
const state = reactive({
    id: '',
    review: 'officialDocs',
    formListData: { id: '', status: '' },
    type: '',
    showType: ''
})
const changeTabs = (item) => {
    state.review = item.key
}


async function initPage() {
    uni.showToast({
        title: '加载中...',
        icon: "loading"
    })
    const { data } = await http.get("/cloud/official-doc/document/get", { id: state.id })
    state.formListData = data
    officialDocs.setFormListData(data)
    uni.hideToast()
}
// 编辑修改
const handleSubmitUpadte = async () => {
    const params = {
        ...officialDocs.submitFormData
    }
    await http.post("/cloud/official-doc/document/update", params).then(({ message }) => {
        uni.showToast({
            title: message,
            icon: "none"
        })
        handleSubmit('agre')
    })
}
function clickLeft() {
    navigateTo({
        url: '/apps/officialDocs/manage/index',
        query: {
            type: state.type,
            showType: state.showType,
        }
    })
}
// 同意 回退
const handleSubmit = (operate) => {
    navigateTo({
        url: '/apps/officialDocs/agreeOperate/index',
        query: {
            operate,
            type: state.type,
            showType: state.showType,
            id: state.id,
        }
    })
}
onLoad((item) => {
    Object.keys(item).forEach((key) => {
        item[key] = decodeURIComponent(item[key])
    })
    if (item.clear) {
        officialDocs.setSubmitFormClearKey()
    }
    state.id = item.id || ''
    state.type = item.type
    state.showType = item.showType
    initPage()
})

onMounted(() => {
    setTimeout(() => {
        // #ifdef H5-WEIXIN || H5
        const { officialDocs } = useStore()
        document.title = officialDocs.getFormListData.statusCode || ''
        // #endif
    }, 500)

})

</script>

<style lang='scss' scoped>
.review-draft {
    height: 100vh;
    background-color: #F6F6F6;

    .footer {
        position: fixed;
        left: 0;
        bottom: 0;
        box-sizing: border-box;
        background: $uni-text-color-inverse;
        width: 100vw;
        padding: 23rpx 30rpx 47rpx;
        text-align: right;

        &.download {
            display: flex;
            justify-content: space-evenly;

            .btn {
                text-align: center;
                font-weight: 400;
                font-size: 24rpx;
                color: #333333;
            }
        }

        .mini-btn {
            margin: 0 10rpx;
            padding-top: 6rpx;
            padding-bottom: 6rpx;
            border: 1px solid $uni-text-color-grey;
            background-color: $uni-text-color-inverse;

            &[type="primary"] {
                background-color: $uni-color-primary;
                border: 1px solid $uni-color-primary
            }

        }
    }
}
</style>