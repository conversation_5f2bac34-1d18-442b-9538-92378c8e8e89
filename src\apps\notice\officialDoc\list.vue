<!-- 公文传阅 -->
<template>
    <view class="announcement">
        <view class="announcement-item" v-for="(item, index) in dataList" :key="index" @click="onConfirm(item)">
            <template v-if="paramsForm.type == 'publish'">
                <view class="cell-item-hander">
                    <text class="two_ellipsis">{{ item.title }}</text>
                </view>
                <view class="cell-item-content">
                    {{ item.identityUserName }} 发布于
                    <text :class="{ active: !item.status }"> {{ item.timerDate }}</text>
                </view>
                <view class="cell-item-content" v-if="item.status">
                    <text> 浏览 {{ item.viewUsers }}/{{ item.receiveUsers }} </text>
                </view>
                <view class="cell-item-footer">
                    <view class="icon">
                        <!-- <uni-icons v-if="item.isTop" type="arrow-up" size="20" color="#11C685"></uni-icons> -->
                        <image class="isTop" v-if="item.isTop" src="@nginx/workbench/notice/isTop.png" />
                    </view>
                    <uni-list-item :class="{ resetListItemActive: !item.status, isRetractActive: item.isRetract }" :rightText="item.statusRemark" />
                </view>
            </template>
            <template v-else>
                <view class="cell-item-hander">
                    <text class="two_ellipsis">{{ item.title }}</text>
                </view>
                <view class="cell-item-content"> {{ item.identityUserName }}发布于{{ item.timerDate }} </view>
                <view class="cell-item-footer" v-if="item.isNeedConfirm">
                    <i />
                    <uni-list-item :class="{ resetConfimItemActive: item.isConfirm }" :showArrow="!item.isConfirm" :rightText="!item.isConfirm ? '去确认' : '已确认'" />
                </view>
            </template>
        </view>
    </view>
</template>

<script setup>
const emit = defineEmits(["complete", "reload"])
const props = defineProps({
    paramsForm: {
        type: Object,
        default: () => {}
    },
    dataList: {
        type: Array,
        default: () => []
    }
})

const dataList = computed(() => props.dataList)

const initPage = (pageNo, pageSize, isAll) => {
    const { type, startTime, endTime } = props.paramsForm
    const params = {
        identifier: "officialDoc",
        keyWords: "",
        startTime,
        endTime,
        pageNo,
        pageSize
    }
    // 我收到的
    let url = "/cloud/mobile/mess/receive/page"
    // 我发布的
    if (type == "publish") {
        url = "/cloud/mobile/mess/publish/page"
    }
    // 所有我发布的
    if (isAll) {
        url = "/app/mobile/mess/publish/page/all"
    }
    http.post(url, params).then(({ data }) => {
        emit("complete", data.list)
    })
}
// 去确认、详情
const onConfirm = (item) => {
    if (!item.isView) {
        emit("reload")
    }
    navigateTo({
        url: "/apps/notice/officialDoc/details",
        query: {
            id: item.id,
            type: props.paramsForm.type,
            receiveUsers: item.receiveUsers,
            contentType: item.contentType,
            messType: item.messType
        }
    })
}

watch(
    () => props.paramsForm,
    () => {
        emit("reload")
    },
    {
        deep: true
    }
)
onLoad(() => {
    emit("reload")
})

defineExpose({ initPage })
</script>

<style lang="scss" scoped>
$color6: #666666;
$warning: #faad14;
$red: red;

.announcement {
    padding: 0rpx 30rpx 30rpx 30rpx;

    .announcement-item {
        background: $uni-bg-color;
        border-radius: 20rpx;
        margin: 20rpx 0;
        padding: 30rpx;

        .cell-item-hander {
            font-size: 30rpx;
            font-weight: 600;
            display: flex;
            align-items: flex-start;
            .list-title-icon {
                display: inline-block;
                font-size: 30rpx;
                padding: 0 6rpx;
                color: $red;
                border-radius: 8rpx;
                min-width: 80rpx;
                width: 80rpx;
                height: 40rpx;
                line-height: 40rpx;
                text-align: center;
                border: 0.1rpx solid $red;
                transform: scale(0.68);
                margin: 8rpx 10rpx 0 0;
            }
        }

        .cell-item-content {
            font-size: 24rpx;
            margin: 20rpx 0;
            color: $color6;

            .active {
                color: $warning;
            }
        }

        .cell-item-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;

            .icon {
                display: flex;
                align-items: center;

                .isTop {
                    width: 32rpx;
                    height: 32rpx;
                    margin-right: 10rpx;
                }

                .screenDomination {
                    color: var(--primary-color);
                    font-size: 20rpx;
                    background-color: #00b7811a;
                    border-radius: 10rpx;
                    padding: 6rpx;
                }
            }

            :deep(.uni-list-item) {
                .uni-list--border {
                    display: none;
                }

                &.resetConfimItemActive {
                    .uni-list-item__extra span,
                    .uni-icon-wrapper {
                        color: $uni-text-color-grey;
                    }
                }

                &.resetListItemActive {
                    .uni-list-item__extra span,
                    .uni-icon-wrapper {
                        color: $warning;
                    }
                }

                &.isRetractActive {
                    .uni-list-item__extra span,
                    .uni-icon-wrapper {
                        color: $uni-text-color-grey;
                    }
                }

                .uni-list-item__container {
                    padding: 0;
                }

                .uni-list-item__extra span,
                .uni-icon-wrapper {
                    color: var(--primary-color);
                    padding: 0;
                    font-size: 28rpx;
                }
            }
        }
    }
}
</style>
